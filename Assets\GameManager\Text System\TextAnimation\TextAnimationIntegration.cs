using UnityEngine;
using TMPro;

namespace TextAnimation
{
    /// <summary>
    /// Provides seamless integration with TextMeshPro components.
    /// Handles automatic setup, material management, and component coordination.
    /// </summary>
    public static class TextAnimationIntegration
    {
        /// <summary>
        /// Automatically sets up text animation on a TextMeshPro component.
        /// </summary>
        /// <param name="textComponent">The TextMeshPro component to animate</param>
        /// <param name="autoStart">Whether to start animation immediately</param>
        /// <returns>The created TextAnimationController</returns>
        public static TextAnimationController SetupTextAnimation(TMP_Text textComponent, bool autoStart = true)
        {
            if (textComponent == null)
            {
                Debug.LogError("Cannot setup text animation on null TextMeshPro component");
                return null;
            }
            
            // Check if animation controller already exists
            TextAnimationController controller = textComponent.GetComponent<TextAnimationController>();
            if (controller == null)
            {
                controller = textComponent.gameObject.AddComponent<TextAnimationController>();
            }
            
            // Ensure the text component has a compatible material
            EnsureCompatibleMaterial(textComponent);
            
            if (autoStart && !controller.IsAnimating)
            {
                controller.StartAnimation();
            }
            
            return controller;
        }
        
        /// <summary>
        /// Sets up text animation with preset effects.
        /// </summary>
        /// <param name="textComponent">The TextMeshPro component to animate</param>
        /// <param name="preset">The animation preset to apply</param>
        /// <param name="intensity">Animation intensity</param>
        /// <param name="speed">Animation speed</param>
        /// <returns>The created TextAnimationPresets component</returns>
        public static TextAnimationPresets SetupTextAnimationWithPreset(TMP_Text textComponent, 
            AnimationType preset, float intensity = 1f, float speed = 1f)
        {
            if (textComponent == null)
            {
                Debug.LogError("Cannot setup text animation on null TextMeshPro component");
                return null;
            }
            
            // Setup basic animation controller first
            TextAnimationController controller = SetupTextAnimation(textComponent, false);
            
            // Add presets component
            TextAnimationPresets presets = textComponent.GetComponent<TextAnimationPresets>();
            if (presets == null)
            {
                presets = textComponent.gameObject.AddComponent<TextAnimationPresets>();
            }
            
            // Apply the preset
            presets.Intensity = intensity;
            presets.Speed = speed;
            presets.CurrentPreset = preset;
            
            return presets;
        }
        
        /// <summary>
        /// Ensures the TextMeshPro component has a material compatible with animations.
        /// </summary>
        public static void EnsureCompatibleMaterial(TMP_Text textComponent)
        {
            if (textComponent == null) return;
            
            Material currentMaterial = textComponent.fontSharedMaterial;
            if (currentMaterial == null)
            {
                Debug.LogWarning($"TextMeshPro component on {textComponent.gameObject.name} has no material assigned");
                return;
            }
            
            // Check if material is already compatible
            if (TextAnimationMaterialManager.IsCompatibleMaterial(currentMaterial))
            {
                return;
            }
            
            // Try to find a default TextMeshPro material if current one isn't compatible
            if (!currentMaterial.shader.name.Contains("TextMeshPro"))
            {
                TMP_FontAsset fontAsset = textComponent.font;
                if (fontAsset != null && fontAsset.material != null)
                {
                    textComponent.fontSharedMaterial = fontAsset.material;
                }
                else
                {
                    Debug.LogWarning($"Could not find compatible material for TextMeshPro component on {textComponent.gameObject.name}");
                }
            }
        }
        
        /// <summary>
        /// Removes text animation from a TextMeshPro component.
        /// </summary>
        public static void RemoveTextAnimation(TMP_Text textComponent)
        {
            if (textComponent == null) return;
            
            // Remove animation components
            TextAnimationController controller = textComponent.GetComponent<TextAnimationController>();
            if (controller != null)
            {
                if (Application.isPlaying)
                    Object.Destroy(controller);
                else
                    Object.DestroyImmediate(controller);
            }
            
            TextAnimationPresets presets = textComponent.GetComponent<TextAnimationPresets>();
            if (presets != null)
            {
                if (Application.isPlaying)
                    Object.Destroy(presets);
                else
                    Object.DestroyImmediate(presets);
            }
        }
        
        /// <summary>
        /// Creates a quick shake effect on text.
        /// </summary>
        public static void QuickShake(TMP_Text textComponent, float duration = 0.5f, float intensity = 1f)
        {
            if (textComponent == null) return;
            
            TextAnimationPresets presets = SetupTextAnimationWithPreset(textComponent, AnimationType.Shake, intensity, 2f);
            textComponent.StartCoroutine(QuickEffectCoroutine(presets, duration));
        }
        
        /// <summary>
        /// Creates a quick pulse effect on text.
        /// </summary>
        public static void QuickPulse(TMP_Text textComponent, float duration = 1f, float intensity = 1f)
        {
            if (textComponent == null) return;
            
            TextAnimationPresets presets = SetupTextAnimationWithPreset(textComponent, AnimationType.Pulse, intensity, 1f);
            textComponent.StartCoroutine(QuickEffectCoroutine(presets, duration));
        }
        
        /// <summary>
        /// Creates a typewriter effect on text.
        /// </summary>
        public static void TypewriterEffect(TMP_Text textComponent, float speed = 0.05f)
        {
            if (textComponent == null) return;
            
            TextAnimationPresets presets = SetupTextAnimationWithPreset(textComponent, AnimationType.Typewriter, 1f, speed);
            presets.StartTypewriter();
        }
        
        /// <summary>
        /// Creates a wave reveal effect on text.
        /// </summary>
        public static void WaveRevealEffect(TMP_Text textComponent, float duration = 2f)
        {
            if (textComponent == null) return;
            
            TextAnimationPresets presets = SetupTextAnimationWithPreset(textComponent, AnimationType.Wave, 1f, 1f);
            presets.StartWaveReveal();
        }
        
        /// <summary>
        /// Applies a rainbow color effect to text.
        /// </summary>
        public static void RainbowEffect(TMP_Text textComponent, float speed = 1f)
        {
            if (textComponent == null) return;
            
            TextAnimationPresets presets = SetupTextAnimationWithPreset(textComponent, AnimationType.ColorShift, 1f, speed);
            presets.StartRainbow();
        }
        
        /// <summary>
        /// Coroutine for temporary effects.
        /// </summary>
        private static System.Collections.IEnumerator QuickEffectCoroutine(TextAnimationPresets presets, float duration)
        {
            yield return new WaitForSeconds(duration);
            
            if (presets != null)
            {
                presets.StopAllEffects();
            }
        }
    }
    
    /// <summary>
    /// Extension methods for TextMeshPro components to make animation setup even easier.
    /// </summary>
    public static class TextMeshProAnimationExtensions
    {
        /// <summary>
        /// Adds animation capability to this TextMeshPro component.
        /// </summary>
        public static TextAnimationController AddAnimation(this TMP_Text textComponent, bool autoStart = true)
        {
            return TextAnimationIntegration.SetupTextAnimation(textComponent, autoStart);
        }
        
        /// <summary>
        /// Adds animation with a specific preset to this TextMeshPro component.
        /// </summary>
        public static TextAnimationPresets AddAnimationPreset(this TMP_Text textComponent, 
            AnimationType preset, float intensity = 1f, float speed = 1f)
        {
            return TextAnimationIntegration.SetupTextAnimationWithPreset(textComponent, preset, intensity, speed);
        }
        
        /// <summary>
        /// Removes animation from this TextMeshPro component.
        /// </summary>
        public static void RemoveAnimation(this TMP_Text textComponent)
        {
            TextAnimationIntegration.RemoveTextAnimation(textComponent);
        }
        
        /// <summary>
        /// Applies a quick shake effect to this text.
        /// </summary>
        public static void Shake(this TMP_Text textComponent, float duration = 0.5f, float intensity = 1f)
        {
            TextAnimationIntegration.QuickShake(textComponent, duration, intensity);
        }
        
        /// <summary>
        /// Applies a quick pulse effect to this text.
        /// </summary>
        public static void Pulse(this TMP_Text textComponent, float duration = 1f, float intensity = 1f)
        {
            TextAnimationIntegration.QuickPulse(textComponent, duration, intensity);
        }
        
        /// <summary>
        /// Applies a typewriter effect to this text.
        /// </summary>
        public static void Typewriter(this TMP_Text textComponent, float speed = 0.05f)
        {
            TextAnimationIntegration.TypewriterEffect(textComponent, speed);
        }
        
        /// <summary>
        /// Applies a wave reveal effect to this text.
        /// </summary>
        public static void WaveReveal(this TMP_Text textComponent, float duration = 2f)
        {
            TextAnimationIntegration.WaveRevealEffect(textComponent, duration);
        }
        
        /// <summary>
        /// Applies a rainbow color effect to this text.
        /// </summary>
        public static void Rainbow(this TMP_Text textComponent, float speed = 1f)
        {
            TextAnimationIntegration.RainbowEffect(textComponent, speed);
        }
    }
}
