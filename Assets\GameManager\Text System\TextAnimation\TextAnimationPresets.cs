using UnityEngine;
using System.Collections;

namespace TextAnimation
{
    /// <summary>
    /// Component that provides easy-to-use preset animations for text.
    /// Can be used alongside or instead of TextAnimationController for common effects.
    /// </summary>
    [RequireComponent(typeof(TextAnimationController))]
    public class TextAnimationPresets : MonoBehaviour
    {
        [Header("Preset Settings")]
        [SerializeField] private AnimationType _currentPreset = AnimationType.None;
        [SerializeField] private float _intensity = 1f;
        [SerializeField] private float _speed = 1f;
        [SerializeField] private float _frequency = 1f;
        
        [Header("Typewriter Settings")]
        [SerializeField] private float _typewriterSpeed = 0.05f;
        [SerializeField] private bool _autoStartTypewriter = false;
        
        [Header("Wave Reveal Settings")]
        [SerializeField] private float _waveRevealDuration = 2f;
        [SerializeField] private float _waveWidth = 3f;
        
        private TextAnimationController _controller;
        private Coroutine _currentEffectCoroutine;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            _controller = GetComponent<TextAnimationController>();
        }
        
        private void Start()
        {
            if (_currentPreset != AnimationType.None)
            {
                ApplyPreset(_currentPreset);
            }
            
            if (_autoStartTypewriter && _currentPreset == AnimationType.Typewriter)
            {
                StartTypewriter();
            }
        }
        
        private void OnValidate()
        {
            if (Application.isPlaying && _controller != null)
            {
                ApplyPreset(_currentPreset);
            }
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Applies a preset animation to the text.
        /// </summary>
        public void ApplyPreset(AnimationType preset)
        {
            if (_controller == null) return;
            
            _currentPreset = preset;
            StopCurrentEffect();
            
            AnimationPreset presetData = new AnimationPreset(preset, _intensity, _speed, _frequency);
            presetData.ApplyToController(_controller);
            
            if (!_controller.IsAnimating)
            {
                _controller.StartAnimation();
            }
        }
        
        /// <summary>
        /// Starts a typewriter effect that reveals characters one by one.
        /// </summary>
        public void StartTypewriter()
        {
            if (_controller == null) return;
            
            StopCurrentEffect();
            _currentEffectCoroutine = StartCoroutine(TypewriterEffect());
        }
        
        /// <summary>
        /// Starts a wave reveal effect.
        /// </summary>
        public void StartWaveReveal()
        {
            if (_controller == null) return;
            
            StopCurrentEffect();
            _currentEffectCoroutine = StartCoroutine(WaveRevealEffect());
        }
        
        /// <summary>
        /// Creates a shake emphasis on specific word or character range.
        /// </summary>
        public void ShakeWord(int wordIndex, float duration = 1f, float intensity = 1f)
        {
            if (_controller == null) return;
            
            StartCoroutine(ShakeWordEffect(wordIndex, duration, intensity));
        }
        
        /// <summary>
        /// Creates a shake emphasis on character range.
        /// </summary>
        public void ShakeCharacters(int startIndex, int endIndex, float duration = 1f, float intensity = 1f)
        {
            if (_controller == null) return;
            
            StartCoroutine(ShakeCharactersEffect(startIndex, endIndex, duration, intensity));
        }
        
        /// <summary>
        /// Applies a rainbow color effect.
        /// </summary>
        public void StartRainbow()
        {
            if (_controller == null) return;
            
            TextAnimationUtility.ApplyRainbowEffect(_controller, _speed);
            ApplyPreset(AnimationType.ColorShift);
        }
        
        /// <summary>
        /// Starts a pulse effect.
        /// </summary>
        public void StartPulse()
        {
            ApplyPreset(AnimationType.Pulse);
        }
        
        /// <summary>
        /// Starts a jitter effect.
        /// </summary>
        public void StartJitter()
        {
            ApplyPreset(AnimationType.Jitter);
        }
        
        /// <summary>
        /// Stops all current effects.
        /// </summary>
        public void StopAllEffects()
        {
            StopCurrentEffect();
            if (_controller != null)
            {
                _controller.ResetAnimation();
                _controller.StopAnimation();
            }
        }
        
        /// <summary>
        /// Fades in the text over time.
        /// </summary>
        public void FadeIn(float duration = 1f)
        {
            if (_controller == null) return;
            
            StopCurrentEffect();
            _currentEffectCoroutine = StartCoroutine(FadeInEffect(duration));
        }
        
        /// <summary>
        /// Fades out the text over time.
        /// </summary>
        public void FadeOut(float duration = 1f)
        {
            if (_controller == null) return;
            
            StopCurrentEffect();
            _currentEffectCoroutine = StartCoroutine(FadeOutEffect(duration));
        }
        
        #endregion
        
        #region Private Methods
        
        private void StopCurrentEffect()
        {
            if (_currentEffectCoroutine != null)
            {
                StopCoroutine(_currentEffectCoroutine);
                _currentEffectCoroutine = null;
            }
        }
        
        private IEnumerator TypewriterEffect()
        {
            _controller.ResetAnimation();
            _controller.FadeIntensity = 1f;
            _controller.StartAnimation();
            
            float progress = 0f;
            while (progress < 1f)
            {
                progress += Time.deltaTime / _typewriterSpeed;
                progress = Mathf.Clamp01(progress);
                
                TextAnimationUtility.ApplyTypewriterEffect(_controller, progress);
                
                yield return null;
            }
            
            // Ensure all characters are visible at the end
            TextAnimationUtility.ApplyTypewriterEffect(_controller, 1f);
        }
        
        private IEnumerator WaveRevealEffect()
        {
            _controller.ResetAnimation();
            _controller.StartAnimation();
            
            float progress = 0f;
            while (progress < 1f)
            {
                progress += Time.deltaTime / _waveRevealDuration;
                progress = Mathf.Clamp01(progress);
                
                TextAnimationUtility.ApplyWaveReveal(_controller, progress, _waveWidth);
                
                yield return null;
            }
            
            // Reset to normal state
            _controller.ResetAnimation();
            _controller.SetAllCharactersAnimation(1f, 1f, 1f, 1f);
        }
        
        private IEnumerator ShakeWordEffect(int wordIndex, float duration, float intensity)
        {
            var textInfo = _controller.GetComponent<TMPro.TMP_Text>().textInfo;
            if (wordIndex >= 0 && wordIndex < textInfo.wordCount)
            {
                var wordInfo = textInfo.wordInfo[wordIndex];
                int startIndex = wordInfo.firstCharacterIndex;
                int endIndex = wordInfo.lastCharacterIndex;
                
                yield return StartCoroutine(ShakeCharactersEffect(startIndex, endIndex, duration, intensity));
            }
        }
        
        private IEnumerator ShakeCharactersEffect(int startIndex, int endIndex, float duration, float intensity)
        {
            float originalShakeIntensity = _controller.ShakeIntensity;
            
            TextAnimationUtility.ApplyShakeEmphasis(_controller, startIndex, endIndex, intensity);
            
            yield return new WaitForSeconds(duration);
            
            // Reset to original state
            _controller.ShakeIntensity = originalShakeIntensity;
            _controller.SetAllCharactersAnimation(1f, 1f, 1f, 1f);
        }
        
        private IEnumerator FadeInEffect(float duration)
        {
            _controller.ResetAnimation();
            _controller.FadeIntensity = 1f;
            _controller.StartAnimation();
            
            float progress = 0f;
            while (progress < 1f)
            {
                progress += Time.deltaTime / duration;
                progress = Mathf.Clamp01(progress);
                
                _controller.SetAllCharactersAnimation(1f, 1f, 1f, progress);
                
                yield return null;
            }
            
            _controller.SetAllCharactersAnimation(1f, 1f, 1f, 1f);
        }
        
        private IEnumerator FadeOutEffect(float duration)
        {
            _controller.FadeIntensity = 1f;
            _controller.StartAnimation();
            
            float progress = 1f;
            while (progress > 0f)
            {
                progress -= Time.deltaTime / duration;
                progress = Mathf.Clamp01(progress);
                
                _controller.SetAllCharactersAnimation(1f, 1f, 1f, progress);
                
                yield return null;
            }
            
            _controller.SetAllCharactersAnimation(1f, 1f, 1f, 0f);
        }
        
        #endregion
        
        #region Properties
        
        public AnimationType CurrentPreset
        {
            get => _currentPreset;
            set { _currentPreset = value; ApplyPreset(value); }
        }
        
        public float Intensity
        {
            get => _intensity;
            set { _intensity = value; ApplyPreset(_currentPreset); }
        }
        
        public float Speed
        {
            get => _speed;
            set { _speed = value; ApplyPreset(_currentPreset); }
        }
        
        public float Frequency
        {
            get => _frequency;
            set { _frequency = value; ApplyPreset(_currentPreset); }
        }
        
        #endregion
    }
}
