using UnityEngine;
using System.Globalization; // Para ToTitleCase
using System.Text; // Para ToTitleCase e outras manipulações
using System.Text.RegularExpressions; // Para RemoveExtraSpaces
using System.Collections.Generic; // Para List

public static class StringExtensions
{
    // A sua extensão original
    public static List<string> SplitIntoSentences(this string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return new List<string>();
        }

        List<string> sentences = new List<string>();
        // Regex para encontrar texto dentro de chaves {texto}
        // .*? é non-greedy, para capturar o conteúdo da chave mais interna primeiro se houver aninhamento,
        // ou mais comumente, para parar na primeira chave de fechamento.
        MatchCollection matches = Regex.Matches(text, @"\{(.*?)\}");

        foreach (Match match in matches)
        {
            if (match.Groups.Count > 1)
            {
                // O grupo 1 contém o texto dentro das chaves
                string sentence = match.Groups[1].Value.Trim();
                if (!string.IsNullOrEmpty(sentence))
                {
                    sentences.Add(sentence);
                }
            }
        }
        return sentences;
    }

    /// <summary>
    /// Divide a string em segmentos, identificando texto dentro de chaves `{}` como sentenças configuráveis
    /// e texto fora das chaves como sentenças padrão. Cria objetos SentenceDisplayData para cada segmento.
    /// </summary>
    /// <param name="text">A string original.</param>
    /// <returns>Uma lista de objetos SentenceDisplayData, um para cada frase.</returns>
    ////public static List<SentenceDisplayData> SplitIntoConfigurableSentences(this string text)
    ////{
    ////    List<SentenceDisplayData> configurableSentences = new List<SentenceDisplayData>();
    ////    if (string.IsNullOrEmpty(text))
    ////    {
    ////        return configurableSentences;
    ////    }
////
    ////    // Regex para encontrar texto dentro de chaves {texto}
    ////    MatchCollection matches = Regex.Matches(text, @"\{(.*?)\}");
    ////    int lastIndex = 0;
////
    ////    foreach (Match match in matches)
    ////    {
    ////        // Captura o texto ANTES da chave de abertura atual
    ////        if (match.Index > lastIndex)
    ////        {
    ////            string outerTextBefore = text.Substring(lastIndex, match.Index - lastIndex).Trim();
    ////            if (!string.IsNullOrEmpty(outerTextBefore))
    ////            {
    ////                configurableSentences.Add(new SentenceDisplayData(outerTextBefore, false)); // false: não é de dentro das chaves
    ////            }
    ////        }
////
    ////        // Captura o texto DENTRO das chaves
    ////        if (match.Groups.Count > 1)
    ////        {
    ////            string innerText = match.Groups[1].Value.Trim();
    ////            if (!string.IsNullOrEmpty(innerText))
    ////            {
    ////                configurableSentences.Add(new SentenceDisplayData(innerText, true)); // true: é de dentro das chaves
    ////            }
    ////        }
    ////        lastIndex = match.Index + match.Length;
    ////    }
////
    ////    // Captura qualquer texto restante DEPOIS da última chave de fechamento
    ////    if (lastIndex < text.Length)
    ////    {
    ////        string outerTextAfter = text.Substring(lastIndex).Trim();
    ////        if (!string.IsNullOrEmpty(outerTextAfter))
    ////        {
    ////            configurableSentences.Add(new SentenceDisplayData(outerTextAfter, false));
    ////        }
    ////    }
    ////    return configurableSentences;
    ////}
    /// <summary>
    /// Trunca a string para um comprimento máximo especificado e adiciona um sufixo de truncamento.
    /// </summary>
    /// <param name="text">A string original.</param>
    /// <param name="maxLength">O comprimento máximo da string resultante (incluindo o sufixo).</param>
    /// <param name="truncationSuffix">O sufixo a ser adicionado se a string for truncada (padrão "...").</param>
    /// <returns>A string truncada ou a original se for menor ou igual ao maxLength.</returns>
    public static string Truncate(this string text, int maxLength, string truncationSuffix = "...")
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
        {
            return text;
        }
        if (maxLength <= truncationSuffix.Length)
        {
            return truncationSuffix.Substring(0, maxLength); // Caso o maxLength seja muito pequeno
        }
        return text.Substring(0, maxLength - truncationSuffix.Length) + truncationSuffix;
    }

    /// <summary>
    /// Converte a string para "Title Case" (Primeira Letra De Cada Palavra Maiúscula).
    /// </summary>
    /// <param name="text">A string original.</param>
    /// <returns>A string em formato Title Case.</returns>
    public static string ToTitleCase(this string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return text;
        }
        return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(text.ToLower());
    }

    /// <summary>
    /// Remove espaços em branco duplicados entre as palavras, deixando apenas um.
    /// Também remove espaços no início e no fim.
    /// </summary>
    /// <param name="text">A string original.</param>
    /// <returns>A string com espaços extras removidos.</returns>
    public static string RemoveExtraSpaces(this string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return text;
        }
        return Regex.Replace(text.Trim(), @"\s+", " ");
    }

    /// <summary>
    /// Tenta converter a string para um inteiro. Retorna um valor padrão se a conversão falhar.
    /// </summary>
    /// <param name="text">A string a ser convertida.</param>
    /// <param name="defaultValue">O valor a ser retornado se a conversão falhar (padrão 0).</param>
    /// <returns>O inteiro convertido ou o valor padrão.</returns>
    public static int ToIntOrDefault(this string text, int defaultValue = 0)
    {
        if (int.TryParse(text, out int result))
        {
            return result;
        }
        return defaultValue;
    }

    /// <summary>
    /// Tenta converter a string para um float. Retorna um valor padrão se a conversão falhar.
    /// </summary>
    /// <param name="text">A string a ser convertida.</param>
    /// <param name="defaultValue">O valor a ser retornado se a conversão falhar (padrão 0f).</param>
    /// <returns>O float convertido ou o valor padrão.</returns>
    public static float ToFloatOrDefault(this string text, float defaultValue = 0f)
    {
        if (float.TryParse(text, NumberStyles.Any, CultureInfo.InvariantCulture, out float result))
        {
            return result;
        }
        return defaultValue;
    }

    /// <summary>
    /// Envolve uma palavra-chave na string com tags de cor para Rich Text do Unity.
    /// </summary>
    /// <param name="text">A string original.</param>
    /// <param name="keyword">A palavra-chave a ser destacada.</param>
    /// <param name="color">A cor para destacar a palavra-chave.</param>
    /// <param name="ignoreCase">Se verdadeiro, ignora maiúsculas/minúsculas ao procurar a palavra-chave (padrão true).</param>
    /// <returns>A string com a palavra-chave destacada usando Rich Text.</returns>
    public static string HighlightKeyword(this string text, string keyword, Color color, bool ignoreCase = true)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(keyword))
        {
            return text;
        }

        string colorHex = ColorUtility.ToHtmlStringRGB(color);
        string coloredKeyword = $"<color=#{colorHex}>{keyword}</color>";

        RegexOptions options = ignoreCase ? RegexOptions.IgnoreCase : RegexOptions.None;
        // Usamos Regex.Escape para tratar caracteres especiais na keyword
        return Regex.Replace(text, Regex.Escape(keyword), coloredKeyword, options);
    }

     /// <summary>
    /// Converte uma string para um tipo Enum especificado.
    /// Retorna o valor padrão do Enum se a conversão falhar ou a string for inválida.
    /// </summary>
    /// <typeparam name="TEnum">O tipo do Enum para o qual converter.</typeparam>
    /// <param name="value">A string a ser convertida.</param>
    /// <param name="ignoreCase">Se verdadeiro, ignora maiúsculas/minúsculas (padrão true).</param>
    /// <param name="defaultValue">O valor padrão a ser retornado em caso de falha. Se não especificado, usa o valor padrão do tipo Enum.</param>
    /// <returns>O valor do Enum convertido ou o valor padrão.</returns>
    public static TEnum ToEnum<TEnum>(this string value, bool ignoreCase = true, TEnum? defaultValue = null) where TEnum : struct, System.Enum
    {
        if (string.IsNullOrEmpty(value))
        {
            return defaultValue ?? default(TEnum);
        }

        if (System.Enum.TryParse<TEnum>(value, ignoreCase, out TEnum result))
        {
            return result;
        }
        return defaultValue ?? default(TEnum);
    }
}
