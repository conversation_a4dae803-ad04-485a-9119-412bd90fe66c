#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using TMPro;

namespace TextAnimation.Editor
{
    /// <summary>
    /// Editor utilities for the Text Animation System.
    /// Provides menu items and context menu options for easy setup.
    /// </summary>
    public static class TextAnimationEditor
    {
        #region Menu Items
        
        [MenuItem("GameObject/UI/Text Animation/Add Animation Controller", false, 2001)]
        public static void AddAnimationController()
        {
            GameObject selectedObject = Selection.activeGameObject;
            if (selectedObject == null)
            {
                EditorUtility.DisplayDialog("No Selection", "Please select a GameObject with a TextMeshPro component.", "OK");
                return;
            }
            
            TMP_Text textComponent = selectedObject.GetComponent<TMP_Text>();
            if (textComponent == null)
            {
                EditorUtility.DisplayDialog("Invalid Selection", "Selected GameObject must have a TextMeshPro component.", "OK");
                return;
            }
            
            // Add animation controller
            TextAnimationController controller = TextAnimationIntegration.SetupTextAnimation(textComponent, false);
            
            // Record for undo
            Undo.RegisterCreatedObjectUndo(controller, "Add Text Animation Controller");
            
            // Select the new component
            Selection.activeObject = controller;
            
            Debug.Log($"Added TextAnimationController to {selectedObject.name}");
        }
        
        [MenuItem("GameObject/UI/Text Animation/Add Preset Animation", false, 2002)]
        public static void AddPresetAnimation()
        {
            GameObject selectedObject = Selection.activeGameObject;
            if (selectedObject == null)
            {
                EditorUtility.DisplayDialog("No Selection", "Please select a GameObject with a TextMeshPro component.", "OK");
                return;
            }
            
            TMP_Text textComponent = selectedObject.GetComponent<TMP_Text>();
            if (textComponent == null)
            {
                EditorUtility.DisplayDialog("Invalid Selection", "Selected GameObject must have a TextMeshPro component.", "OK");
                return;
            }
            
            // Show preset selection dialog
            AnimationType selectedPreset = ShowPresetSelectionDialog();
            if (selectedPreset == AnimationType.None) return;
            
            // Add preset animation
            TextAnimationPresets presets = TextAnimationIntegration.SetupTextAnimationWithPreset(
                textComponent, selectedPreset, 1f, 1f);
            
            // Record for undo
            Undo.RegisterCreatedObjectUndo(presets, "Add Text Animation Preset");
            
            // Select the new component
            Selection.activeObject = presets;
            
            Debug.Log($"Added {selectedPreset} animation preset to {selectedObject.name}");
        }
        
        [MenuItem("Tools/Text Animation/Setup All TextMeshPro in Scene", false, 1001)]
        public static void SetupAllTextMeshProInScene()
        {
            TMP_Text[] allTextComponents = Object.FindObjectsOfType<TMP_Text>();
            
            if (allTextComponents.Length == 0)
            {
                EditorUtility.DisplayDialog("No TextMeshPro Found", "No TextMeshPro components found in the current scene.", "OK");
                return;
            }
            
            bool confirmed = EditorUtility.DisplayDialog("Setup All TextMeshPro", 
                $"This will add TextAnimationController to {allTextComponents.Length} TextMeshPro components. Continue?", 
                "Yes", "Cancel");
                
            if (!confirmed) return;
            
            int setupCount = 0;
            foreach (TMP_Text textComponent in allTextComponents)
            {
                if (textComponent.GetComponent<TextAnimationController>() == null)
                {
                    TextAnimationController controller = TextAnimationIntegration.SetupTextAnimation(textComponent, false);
                    Undo.RegisterCreatedObjectUndo(controller, "Setup Text Animation");
                    setupCount++;
                }
            }
            
            Debug.Log($"Setup complete! Added TextAnimationController to {setupCount} TextMeshPro components.");
        }
        
        [MenuItem("Tools/Text Animation/Clear Material Cache", false, 1002)]
        public static void ClearMaterialCache()
        {
            TextAnimationMaterialManager.ClearCache();
            Debug.Log("Text Animation material cache cleared.");
        }
        
        #endregion
        
        #region Context Menu
        
        [MenuItem("CONTEXT/TMP_Text/Add Text Animation")]
        public static void AddTextAnimationContext(MenuCommand command)
        {
            TMP_Text textComponent = (TMP_Text)command.context;
            
            if (textComponent.GetComponent<TextAnimationController>() != null)
            {
                EditorUtility.DisplayDialog("Already Has Animation", "This TextMeshPro component already has a TextAnimationController.", "OK");
                return;
            }
            
            TextAnimationController controller = TextAnimationIntegration.SetupTextAnimation(textComponent, false);
            Undo.RegisterCreatedObjectUndo(controller, "Add Text Animation");
            
            Debug.Log($"Added TextAnimationController to {textComponent.gameObject.name}");
        }
        
        [MenuItem("CONTEXT/TMP_Text/Add Preset Animation")]
        public static void AddPresetAnimationContext(MenuCommand command)
        {
            TMP_Text textComponent = (TMP_Text)command.context;
            
            AnimationType selectedPreset = ShowPresetSelectionDialog();
            if (selectedPreset == AnimationType.None) return;
            
            TextAnimationPresets presets = TextAnimationIntegration.SetupTextAnimationWithPreset(
                textComponent, selectedPreset, 1f, 1f);
            
            Undo.RegisterCreatedObjectUndo(presets, "Add Text Animation Preset");
            
            Debug.Log($"Added {selectedPreset} animation preset to {textComponent.gameObject.name}");
        }
        
        [MenuItem("CONTEXT/TMP_Text/Remove Text Animation")]
        public static void RemoveTextAnimationContext(MenuCommand command)
        {
            TMP_Text textComponent = (TMP_Text)command.context;
            
            TextAnimationController controller = textComponent.GetComponent<TextAnimationController>();
            TextAnimationPresets presets = textComponent.GetComponent<TextAnimationPresets>();
            
            if (controller == null && presets == null)
            {
                EditorUtility.DisplayDialog("No Animation", "This TextMeshPro component doesn't have text animation components.", "OK");
                return;
            }
            
            bool confirmed = EditorUtility.DisplayDialog("Remove Text Animation", 
                "This will remove all text animation components from this TextMeshPro. Continue?", 
                "Yes", "Cancel");
                
            if (!confirmed) return;
            
            if (controller != null)
            {
                Undo.DestroyObjectImmediate(controller);
            }
            
            if (presets != null)
            {
                Undo.DestroyObjectImmediate(presets);
            }
            
            Debug.Log($"Removed text animation from {textComponent.gameObject.name}");
        }
        
        #endregion
        
        #region Validation
        
        [MenuItem("GameObject/UI/Text Animation/Add Animation Controller", true)]
        [MenuItem("GameObject/UI/Text Animation/Add Preset Animation", true)]
        public static bool ValidateTextMeshProSelection()
        {
            GameObject selectedObject = Selection.activeGameObject;
            return selectedObject != null && selectedObject.GetComponent<TMP_Text>() != null;
        }
        
        [MenuItem("CONTEXT/TMP_Text/Add Text Animation", true)]
        public static bool ValidateAddTextAnimation(MenuCommand command)
        {
            TMP_Text textComponent = (TMP_Text)command.context;
            return textComponent.GetComponent<TextAnimationController>() == null;
        }
        
        [MenuItem("CONTEXT/TMP_Text/Remove Text Animation", true)]
        public static bool ValidateRemoveTextAnimation(MenuCommand command)
        {
            TMP_Text textComponent = (TMP_Text)command.context;
            return textComponent.GetComponent<TextAnimationController>() != null || 
                   textComponent.GetComponent<TextAnimationPresets>() != null;
        }
        
        #endregion
        
        #region Helper Methods
        
        private static AnimationType ShowPresetSelectionDialog()
        {
            string[] presetNames = System.Enum.GetNames(typeof(AnimationType));
            int selectedIndex = EditorUtility.DisplayDialogComplex("Select Animation Preset", 
                "Choose an animation preset to apply:", 
                "Shake", "Wave", "Cancel");
                
            switch (selectedIndex)
            {
                case 0: return AnimationType.Shake;
                case 1: return AnimationType.Wave;
                default: return AnimationType.None;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Custom property drawer for AnimationType enum.
    /// </summary>
    [CustomPropertyDrawer(typeof(AnimationType))]
    public class AnimationTypePropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);
            
            // Get the current enum value
            AnimationType currentValue = (AnimationType)property.enumValueIndex;
            
            // Create a dropdown with descriptions
            string[] displayNames = {
                "None - No Animation",
                "Shake - Random Movement",
                "Wave - Sine Wave Motion",
                "Scale - Size Animation",
                "Fade - Alpha Animation",
                "Color Shift - Hue Animation",
                "Typewriter - Character Reveal",
                "Pulse - Scale + Fade",
                "Jitter - Shake + Wave",
                "Custom - Manual Setup"
            };
            
            int selectedIndex = EditorGUI.Popup(position, label.text, property.enumValueIndex, displayNames);
            property.enumValueIndex = selectedIndex;
            
            EditorGUI.EndProperty();
        }
    }
}
#endif