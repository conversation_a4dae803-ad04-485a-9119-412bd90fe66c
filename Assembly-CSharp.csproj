﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_0_42;UNITY_6000_0;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;UNITY_POST_PROCESSING_STACK_V2;DOTWEEN;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.0.42f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\zh-Hant\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\ja\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\ru\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\pl\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\pt-BR\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\ko\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\fr\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\zh-Hans\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\System.Text.Json.SourceGeneration.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\es\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\tr\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\it\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\de\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\Huggable X Horror\Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\cs\System.Text.Json.SourceGeneration.resources.dll" />
    <Analyzer Include="F:\6000.0.42f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="F:\6000.0.42f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="F:\6000.0.42f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
    <Analyzer Include="F:\Huggable X Horror\Library\PackageCache\com.unity.dt.app-ui@166eda91cea7\Runtime\SourceGenerators\netstandard2.0\EnumToLowerCase.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\LookAtTarget.cs" />
    <Compile Include="Assets\Inventory\Core\Interface\UI\IInventory Slot UI.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimplePlayerController2D.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventHandler.cs" />
    <Compile Include="Assets\Props\Items\Scriptable Objects dos items\Consumable Item SO.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimplePlayerController.cs" />
    <Compile Include="Assets\GameManager\Scene\Scriptable Object Scene\Scene Data Base SO.cs" />
    <Compile Include="Assets\GameManager\Scene\Scene Datas Struct\Spawn Point System\Spawn Point.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\EnvMapAnimator.cs" />
    <Compile Include="Assets\GameManager\Scene\Scene Datas Struct\Local Scene.cs" />
    <Compile Include="Assets\Scripts Utils\Mesh Utility.cs" />
    <Compile Include="Assets\GameManager\Scripts\SingletonPattern.cs" />
    <Compile Include="Assets\Scripts Utils\Transform Scripts\Transform Utils.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Json Converters\Asset Reference Converter.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_A.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\User Feedback.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\UserMonoBehaviour.cs" />
    <Compile Include="Assets\GameManager\Save\Save Files Manager.cs" />
    <Compile Include="Assets\GameManager\Events\Callback Item.cs" />
    <Compile Include="Assets\Inventory\Core\Player\Basic Inventory Manager.cs" />
    <Compile Include="Assets\Inventory\Core\Grid Inventory\Key Journal Inventory Slot.cs" />
    <Compile Include="Assets\Inventory\Core\Grid Inventory\Item Data To Rearrange.cs" />
    <Compile Include="Assets\Samples\Addressables\2.3.16\Custom Build and Playmode Scripts\LoadSceneForCustomBuild.cs" />
    <Compile Include="Assets\Inventory\Core\UI\Inventory UI.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimplePlayerOnSurface.cs" />
    <Compile Include="Assets\GameManager\Text System\TextAnimation\Examples\TextAnimationDemo.cs" />
    <Compile Include="Assets\Inventory\Core\Basic Inventory\Inventory.cs" />
    <Compile Include="Assets\Samples\Behavior\1.0.10\Runtime Serialization\ChooseTargetPosition.cs" />
    <Compile Include="Assets\GameManager\Scene\Scene Datas Struct\Spawn Point System\SpawnPointManager.cs" />
    <Compile Include="Assets\Inventory\Core\Interface\UI\IInventory UI.cs" />
    <Compile Include="Assets\GameManager\Scene\Transition Trigger.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark02.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Json Serializer Settings.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player FPS Controller\Camera\Head Bob struct.cs" />
    <Compile Include="Assets\GameManager\OptionalPropertyDrawer.cs" />
    <Compile Include="Assets\GameManager\Text System\TextAnimation\TextAnimationIntegration.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\ExemploCurrentActionMap.cs" />
    <Compile Include="Assets\Scripts Utils\Camera Utils\Camera Utils.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Input System Samples\Split Screen Multiplayer\ConstantOrbitalRotation.cs" />
    <Compile Include="Assets\Props\Scripts\Interactions\Interactable Object.cs" />
    <Compile Include="Assets\Player\Scripts\Player UI\Hud.cs" />
    <Compile Include="Assets\GameManager\Events\Callback Config.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\CinemachineFadeOutShaderController.cs" />
    <Compile Include="Assets\GameManager\Input System\Examples\UnregisterIEnumeratorExample.cs" />
    <Compile Include="Assets\Scripts Utils\Utils Functions.cs" />
    <Compile Include="Assets\Inventory\Core\Grid Inventory\Grid Inventory Slot.cs" />
    <Compile Include="Assets\GameManager\Interfaces\IGameEntity.cs" />
    <Compile Include="Assets\GameManager\Scripts\GameManager script.cs" />
    <Compile Include="Assets\Player\Scripts\Player Utils.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\TurnAroundPlayer.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\User Menu Manager.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\MagnetGroupController.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Hint\Runtime\Hint Icon Info.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Unity Event System\Parameter Data.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Player Conditions\Status Condition SO.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimplePlayerAimController.cs" />
    <Compile Include="Assets\Scripts Utils\Camera Utils\Camera Tools.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\Movement Effects\ExhaustionEffect.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Input System Samples\Split Screen Multiplayer\PlayerCounter.cs" />
    <Compile Include="Assets\GameManager\Scene\Scene Datas Struct\Spawn Point System\Spawn Point Manager Editor.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Utils.cs" />
    <Compile Include="Assets\Props\Scripts\Save\stateful object.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Context Factory.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Generic Condition SO.cs" />
    <Compile Include="Assets\Modified Unity Components Functions\Custom Instantiate.cs" />
    <Compile Include="Assets\GameManager\Save\UI\Save Slot UI.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Condition System Reflection\My Custom Condition.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\Input Action Config.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\Action Activation Context.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\PlayerStatus.cs" />
    <Compile Include="Assets\GameManager\Text System\TextAnimation\TextAnimationData.cs" />
    <Compile Include="Assets\Scripts Utils\String\String Extensions.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\MixCamerasBasedOnSpeed.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_ExampleScript_01.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMPro_InstructionOverlay.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player FPS Controller\Camera\Head Bob Manager.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeB.cs" />
    <Compile Include="Assets\GameManager\Custom Scriptable Object System\InstantiableSO.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\High Heart Rate Effect.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Heart Rate Data.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\PlayerMonoBehaviour.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\Player Input Handler Blocks.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\ReparentPlayerToSurface.cs" />
    <Compile Include="Assets\GameManager\Scripts\MultitionPattern.cs" />
    <Compile Include="Assets\GameManager\Audio System\AudioEffect.cs" />
    <Compile Include="Assets\Inventory\Core\Basic Inventory\Inventory Slot.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player FPS Controller\Movement\PlayerMovimento.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\ReduceGroupWeightWhenBelowFloor.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexJitter.cs" />
    <Compile Include="Assets\Props\Items\Itens Equipaveis\Ferramentas\Lanterna\Lanterna.cs" />
    <Compile Include="Assets\Samples\Behavior\1.0.10\Runtime Serialization\SerializationExampleSceneController.cs" />
    <Compile Include="Assets\Props\Items\Scriptable Objects dos items\Readable Item SO.cs" />
    <Compile Include="Assets\GameManager\Finite State Machine\Player\Player Basic States.cs" />
    <Compile Include="Assets\Props\Items\Scriptable Objects dos items\Item Type.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_UiFrameRateCounter.cs" />
    <Compile Include="Assets\Inventory\Core\Interface\IInventory.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexZoom.cs" />
    <Compile Include="Assets\Modified Unity Components Functions\GridLayout Group Stretchable.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Unity Event System\Listener Data.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\Magnet.cs" />
    <Compile Include="Assets\GameManager\Finite State Machine\Base State.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_B.cs" />
    <Compile Include="Assets\GameManager\Audio System\SimpleAudioEffect.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Condition System\Input Condition SO.cs" />
    <Compile Include="Assets\Scripts Utils\Layer Utility.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\ExemploCallbacksDiretos.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Condition System Reflection\Listener Condition.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Events with Scriptable Object\Generic Event SO.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SkewTextExample.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\PlayersManager.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\WarpTextExample.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player FPS Controller\Camera\FPS Utils.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Json Converters\Vectors Converter.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\StatusEffect.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\User UI Manager.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ShaderPropAnimator.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Interaction Attribute.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\WrapAround.cs" />
    <Compile Include="Assets\Props\Items\Scriptable Objects dos items\Item SO.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimplePlayerShoot.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Contract Resolver\Only Classes Contract Resolver.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\FlyAround.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Events with Scriptable Object\Player Events\UI Events\Instantiate UI Element.cs" />
    <Compile Include="Assets\Scripts Utils\GameObject Utils\RectMask3DControllerEditor.cs" />
    <Compile Include="Assets\Player\Scripts\Player IK\IK do Player.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\LockPosZ.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\Input Event Binding Finder.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Serialized Unity Scripts\Serializable Transform.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\ExpandingAimReticle.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Sanity Data.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextInfoDebugTool.cs" />
    <Compile Include="Assets\Props\Model Info.cs" />
    <Compile Include="Assets\Samples\Behavior\1.0.10\Unity Behavior Example\Actions\TalkAction.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\PlayerMonobehaviour Funcs.cs" />
    <Compile Include="Assets\Inventory\Core\Grid Inventory\Grid Inventory.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\ThirdPersonFollowCameraSideSwapper.cs" />
    <Compile Include="Assets\Scripts Utils\Image Utils\Image Utils.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimpleCarController.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeA.cs" />
    <Compile Include="Assets\Player\Scripts\Player IK\DynamicIKManager.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Hint\Runtime\Device Group Hint Config.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Player Conditions\Stamina Condition SO.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\TiredEffect.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Json Converters\Quaternion Converter.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\EffectManager.cs" />
    <Compile Include="Assets\GameManager\Save\Save System Testers\Test GameObject Save.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Hint\Runtime\Input Hint Service.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01_UGUI.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SimpleScript.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Serialized Unity Scripts\Serializable Date Time and Time Span.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark04.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Hint\Runtime\Bind Hint Config.cs" />
    <Compile Include="Assets\Scripts Utils\TMPro Utility.cs" />
    <Compile Include="Assets\GameManager\Events\Callback Config Base.cs" />
    <Compile Include="Assets\Scripts Utils\Rect Scripts\RectTransform Utils.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ChatController.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\DampedTracker.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\AimTargetManager.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Stamina Data.cs" />
    <Compile Include="Assets\GameManager\Input System\Player Inputs Config.cs" />
    <Compile Include="Assets\Props\Items\Item Examine.cs" />
    <Compile Include="Assets\Props\Scripts\Interactions\Interact Hint.cs" />
    <Compile Include="Assets\Custom Attributes\Subclass Selector.cs" />
    <Compile Include="Assets\GameManager\Addresables\AddressableAssetExtension.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_DigitValidator.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Hint\Runtime\Input Hints Config.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_FrameRateCounter.cs" />
    <Compile Include="Assets\TutorialInfo\Scripts\Readme.cs" />
    <Compile Include="Assets\Scripts Utils\Rect Scripts\Mesh Rect Adapter.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\PlayerInputHandler.Registration.cs" />
    <Compile Include="Assets\GameManager\Finite State Machine\IState.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Base Condition SO.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshSpawner.cs" />
    <Compile Include="Assets\Inventory\Core\Interface\Grid\IInventory Grid Slot.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\Input State.cs" />
    <Compile Include="Assets\GameManager\Save\SaveManager.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SpawnInRadius.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Player Conditions\Heart Rate Condition SO.cs" />
    <Compile Include="Assets\GameManager\Text System\TextAnimation\TextAnimationPresets.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\CursorLockManager.cs" />
    <Compile Include="Assets\Props\Scripts\Interactions\Generic Interactable.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Unity Event System\Return Data.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimpleBullet.cs" />
    <Compile Include="Assets\Inventory\Core\Interface\IInventory Slot.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Contract Resolver\Ignore Classes Contract Resolver.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Player Conditions\Sanity Condition SO.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Json Converters\Transform Converter.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\CameraController.cs" />
    <Compile Include="Assets\GameManager\Custom Scriptable Object System\InstanceSODrawer.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark03.cs" />
    <Compile Include="Assets\Player\Scripts\Player IK\IKTargetConfigurator.cs" />
    <Compile Include="Assets\Scripts Utils\Rect Scripts\UI Attacher.cs" />
    <Compile Include="Assets\Props\Scripts\Save\SaveableComponent.cs" />
    <Compile Include="Assets\Scripts Utils\Scene Utils\Scene Utils.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\BreathlessEffect.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Health Data.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Input System Samples\Split Screen Multiplayer\PlayerInitializer.cs" />
    <Compile Include="Assets\Custom Attributes\Create Instance SO.cs" />
    <Compile Include="Assets\Inventory\Core\UI\Inventory Slot UI.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\Input Call Config.cs" />
    <Compile Include="Assets\Inventory\Core\Interface\Grid\IInventory Grid.cs" />
    <Compile Include="Assets\Scripts Utils\GameObject Utils\Rect Mask 3D Controller.cs" />
    <Compile Include="Assets\Props\Items\Scriptable Objects dos items\Key Item SO.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Input System Samples\Split Screen Multiplayer\CustomInputHandler.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Events with Scriptable Object\Base Event SO.cs" />
    <Compile Include="Assets\Player\Scripts\Player UI\CrosshairHelper.cs" />
    <Compile Include="Assets\GameManager\Save\UI\Load Game Panel Manager.cs" />
    <Compile Include="Assets\Props\Scripts\Save\ISaveable.cs" />
    <Compile Include="Assets\Props\Items\Scriptable Objects dos items\Equipable Item SO.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\DropdownSample.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexColorCycler.cs" />
    <Compile Include="Assets\Event System\Event System With Scriptable Object\Condition with Scriptable Object\Player Conditions\Health Condition SO.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\ShotQualityBasedOnSplineCartPosition.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\Movement Effects\AdrenalineRushEffect.cs" />
    <Compile Include="Assets\GameManager\Scene\SceneManager script.cs" />
    <Compile Include="Assets\Samples\Behavior\1.0.10\Unity Behavior Example\Actions\SetRandomTargetAction.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\User MonoBehaviour Get interface.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SimplePlayerAnimator.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\AimCameraRig.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\PlayerInputHandler.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\Movement Effects\MovementEffectManager.cs" />
    <Compile Include="Assets\GameManager\Interfaces\IPlayer.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\RandomizedDollySpeed.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ObjectSpin.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_PhoneNumberValidator.cs" />
    <Compile Include="Assets\GameManager\User\User Scripts\Examples\UIMapSwitchExample.cs" />
    <Compile Include="Assets\Props\Scripts\Door\Door.cs" />
    <Compile Include="Assets\Scripts Utils\GameObject Utils\GameObject Utils.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\Movement Effects\PanicEffect.cs" />
    <Compile Include="Assets\Props\Props Interface.cs" />
    <Compile Include="Assets\GameManager\Scripts\Item List.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextConsoleSimulator.cs" />
    <Compile Include="Assets\GameManager\Input System\Player\ExemploSendMessages.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TeleType.cs" />
    <Compile Include="Assets\Props\Scripts\Hideout\Hideout.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventCheck.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Hint\Runtime\Scheme Hint Config.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Serialized Unity Scripts\Serializable Vectors.cs" />
    <Compile Include="Assets\GameManager\Text System\TextAnimation\TextAnimationController.cs" />
    <Compile Include="Assets\GameManager\Save\Save Data Struct.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player FPS Controller\Movement\MovementStatusModifier.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\Dizzines Effect.cs" />
    <Compile Include="Assets\GameManager\Input System\Examples\IEnumeratorCallbackExample.cs" />
    <Compile Include="Assets\GameManager\Audio System\MultiClipAudioEffect.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshProFloatingText.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SamplesHelpUI.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player FPS Controller\Camera\FPSController.cs" />
    <Compile Include="Assets\GameManager\Interfaces\INPC.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\RigidbodyInterpolationSetter.cs" />
    <Compile Include="Assets\GameManager\Input System\Input Interactions\My Holding Timer.cs" />
    <Compile Include="Assets\Player\Scripts\Player UI\CrosshairManager.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\RunnerController.cs" />
    <Compile Include="Assets\Props\Items\Item Collectable.cs" />
    <Compile Include="Assets\Player\Scripts\Player Basic\Player Status\Status Effects\Low Sanity Trip Effect.cs" />
    <Compile Include="Assets\GameManager\Main Menu\Main Menu.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Unity Event System\Custom Unity Event Utils.cs" />
    <Compile Include="Assets\GameManager\Events\Method Data.cs" />
    <Compile Include="Assets\GameManager\Json Scripts\Serialized Unity Scripts\Serializable GameObject.cs" />
    <Compile Include="Assets\GameManager\Scripts\Physics Manager.cs" />
    <Compile Include="Assets\Scripts Utils\Transform Scripts\Transform Utils Monobehaviour.cs" />
    <Compile Include="Assets\GameManager\Events\Triggers\Vision Event Trigger.cs" />
    <Compile Include="Assets\Event System\Event System With System Reflection\My Unity Event.cs" />
    <Compile Include="Assets\Scripts Utils\Rect Scripts\Mesh Rect Adapter Editor.cs" />
    <Compile Include="Assets\Samples\Behavior\1.0.10\Runtime Serialization\Weapon.cs" />
    <Compile Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\Scripts\SamplesDynamicUI.cs" />
    <Compile Include="Assets\GameManager\Text System\TextAnimation\TextAnimationMaterialManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\Packages\System.Text.Json.9.0.3\lib\netstandard2.0\System.Text.Json.xml" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\ko\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.IO.Pipelines.9.0.3\THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets\Packages\System.Text.Encodings.Web.9.0.3\lib\netstandard2.0\System.Text.Encodings.Web.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\pl\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\es\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Roboto-Bold - License.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\ja\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\ko\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\zh-Hant\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Unity - OFL.txt" />
    <None Include="Assets\Shaders\Text Shaders\TMP_SDF_Shake.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\pl\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\Newtonsoft.Json.13.0.3\lib\netstandard2.0\Newtonsoft.Json.dll" />
    <None Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\UI\SampleHelpUI.uxml" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\ja\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\it\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Oswald-Bold - OFL.txt" />
    <None Include="Assets\- Classic 64 Asset Pack 0.6\Rocks\- Read Me.txt" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Roboto-Bold - AFL.txt" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Anton OFL.txt" />
    <None Include="Assets\Packages\System.IO.Pipelines.9.0.3\useSharedDesignerContext.txt" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\fr\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\lib\netstandard2.0\System.Text.Json.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\de\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\zh-Hans\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\tr\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Samples\Addressables\2.3.16\Custom Build and Playmode Scripts\README.txt" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\System.Text.Json.SourceGeneration.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\pt-BR\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\cs\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\tr\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Samples\Cinemachine\3.1.3\Shared Assets\UI\SamplesUIStyle.uss" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\de\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\SDFFunctions.hlsl" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\tr\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll" />
    <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.xml" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\System.Text.Json.SourceGeneration.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\ja\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.IO.Pipelines.9.0.3\LICENSE.TXT" />
    <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\useSharedDesignerContext.txt" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\LICENSE.TXT" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\it\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\- Classic 64 Asset Pack 0.6\Readme.txt" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\cs\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\Shaders\Text Shaders\TMP_SDF_Wave.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\pt-BR\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\Packages\System.IO.Pipelines.9.0.3\lib\netstandard2.0\System.IO.Pipelines.xml" />
    <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\ru\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\LICENSE.TXT" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\pt-BR\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\Packages\System.Text.Encodings.Web.9.0.3\useSharedDesignerContext.txt" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\fr\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\de\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\Shaders\TMP_SDF_Shake.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\it\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Shaders\Text Shaders\TMP_AnimatedText.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\zh-Hans\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\ko\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\Packages\Newtonsoft.Json.13.0.3\lib\netstandard2.0\Newtonsoft.Json.xml" />
    <None Include="Assets\Packages\System.IO.Pipelines.9.0.3\lib\netstandard2.0\System.IO.Pipelines.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\ru\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Encodings.Web.9.0.3\LICENSE.TXT" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\es\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Scripts Utils\GameObject Utils\3D UI GameObject Unlit.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\ru\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Scripts Utils\GameObject Utils\3D UI GameObject Lit.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\es\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\System.Text.Json.SourceGeneration.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn3.11\cs\zh-Hant\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\LICENSE.TXT" />
    <None Include="Assets\- Classic 64 Asset Pack 0.6\Changelog v0.6.txt" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\zh-Hans\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\cs\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\useSharedDesignerContext.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\zh-Hant\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.4\cs\fr\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\analyzers\dotnet\roslyn4.0\cs\pl\System.Text.Json.SourceGeneration.resources.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.xml" />
    <None Include="Assets\Packages\System.Text.Encodings.Web.9.0.3\THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets\Packages\System.Text.Encodings.Web.9.0.3\lib\netstandard2.0\System.Text.Encodings.Web.xml" />
    <None Include="Assets\Packages\System.Text.Json.9.0.3\useSharedDesignerContext.txt" />
    <None Include="Assets\- Classic 64 Asset Pack 0.6\Nature\Read Me.txt" />
    <None Include="Assets\Shaders\Alway On Top Unlit.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\THIRD-PARTY-NOTICES.TXT" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Bangers - OFL.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>F:\6000.0.42f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web">
      <HintPath>Assets\Packages\System.Text.Encodings.Web.9.0.3\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@60ef35ffd3cd\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Assets\Packages\Newtonsoft.Json.13.0.3\lib\netstandard2.0\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Json">
      <HintPath>Assets\Packages\System.Text.Json.9.0.3\lib\netstandard2.0\System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.3\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@b4d700247d4b\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines">
      <HintPath>Assets\Packages\System.IO.Pipelines.9.0.3\lib\netstandard2.0\System.IO.Pipelines.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>F:\6000.0.42f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AppUI.Navigation.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.AppUI.Navigation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Alembic.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Formats.Alembic.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Cinemachine.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Cinemachine.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx">
      <HintPath>Library\ScriptAssemblies\Autodesk.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Bindings.OpenImageIO.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Bindings.OpenImageIO.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library\ScriptAssemblies\PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Recorder.Base">
      <HintPath>Library\ScriptAssemblies\Unity.Recorder.Base.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder.AddOns.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.AddOns.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Behavior.Serialization.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Behavior.Serialization.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Splines.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Splines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder.AssetIdRemapUtility">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.AssetIdRemapUtility.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Profiling.Core">
      <HintPath>Library\ScriptAssemblies\Unity.Profiling.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.Updater.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Muse.Behavior">
      <HintPath>Library\ScriptAssemblies\Unity.Muse.Behavior.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AppUI.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.AppUI.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Postprocessing.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx.Editor">
      <HintPath>Library\ScriptAssemblies\Autodesk.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SerializableGUID">
      <HintPath>Library\ScriptAssemblies\SerializableGUID.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Animation.Rigging">
      <HintPath>Library\ScriptAssemblies\Unity.Animation.Rigging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AppUI.Redux.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.AppUI.Redux.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Recorder.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Recorder.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Sequences.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Sequences.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Animation.Rigging.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Animation.Rigging.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Recorder">
      <HintPath>Library\ScriptAssemblies\Unity.Recorder.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ResourceManager">
      <HintPath>Library\ScriptAssemblies\Unity.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Sequences">
      <HintPath>Library\ScriptAssemblies\Unity.Sequences.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Splines">
      <HintPath>Library\ScriptAssemblies\Unity.Splines.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Cinemachine">
      <HintPath>Library\ScriptAssemblies\Unity.Cinemachine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Behavior.Serialization">
      <HintPath>Library\ScriptAssemblies\Unity.Behavior.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Behavior.GraphFramework">
      <HintPath>Library\ScriptAssemblies\Unity.Behavior.GraphFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Behavior">
      <HintPath>Library\ScriptAssemblies\Unity.Behavior.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AppUI">
      <HintPath>Library\ScriptAssemblies\Unity.AppUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Behavior.Authoring">
      <HintPath>Library\ScriptAssemblies\Unity.Behavior.Authoring.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.EditorCoroutines.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.EditorCoroutines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NuGetForUnity">
      <HintPath>Library\ScriptAssemblies\NuGetForUnity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ProBuilder">
      <HintPath>Library\ScriptAssemblies\Unity.ProBuilder.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Formats.Fbx.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Behavior.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Behavior.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Localization.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Localization.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Alembic.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Formats.Alembic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Formats.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Localization">
      <HintPath>Library\ScriptAssemblies\Unity.Localization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="InputHintEditor.csproj" />
    <ProjectReference Include="XNode.csproj" />
    <ProjectReference Include="UnityEngine.InputSystem.Samples.UIvsGameInput.csproj" />
    <ProjectReference Include="XNodeEditor.csproj" />
    <ProjectReference Include="Unity.InputSystem.InGameHints.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
