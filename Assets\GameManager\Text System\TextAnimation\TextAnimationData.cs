using UnityEngine;
using System;

namespace TextAnimation
{
    /// <summary>
    /// Data structure for per-character animation settings.
    /// </summary>
    [Serializable]
    public struct CharacterAnimationData
    {
        [Range(0f, 1f)] public float shakeIntensity;
        [Range(0f, 1f)] public float waveIntensity;
        [Range(0f, 1f)] public float scaleIntensity;
        [Range(0f, 1f)] public float fadeIntensity;
        
        public CharacterAnimationData(float shake = 1f, float wave = 1f, float scale = 1f, float fade = 1f)
        {
            shakeIntensity = Mathf.Clamp01(shake);
            waveIntensity = Mathf.Clamp01(wave);
            scaleIntensity = Mathf.Clamp01(scale);
            fadeIntensity = Mathf.Clamp01(fade);
        }
        
        /// <summary>
        /// Converts the animation data to a Color32 for vertex color storage.
        /// </summary>
        public Color32 ToColor32()
        {
            return new Color32(
                (byte)(shakeIntensity * 255),
                (byte)(waveIntensity * 255),
                (byte)(scaleIntensity * 255),
                (byte)(fadeIntensity * 255)
            );
        }
        
        /// <summary>
        /// Creates animation data from a Color32.
        /// </summary>
        public static CharacterAnimationData FromColor32(Color32 color)
        {
            return new CharacterAnimationData(
                color.r / 255f,
                color.g / 255f,
                color.b / 255f,
                color.a / 255f
            );
        }
        
        /// <summary>
        /// Lerps between two animation data sets.
        /// </summary>
        public static CharacterAnimationData Lerp(CharacterAnimationData a, CharacterAnimationData b, float t)
        {
            return new CharacterAnimationData(
                Mathf.Lerp(a.shakeIntensity, b.shakeIntensity, t),
                Mathf.Lerp(a.waveIntensity, b.waveIntensity, t),
                Mathf.Lerp(a.scaleIntensity, b.scaleIntensity, t),
                Mathf.Lerp(a.fadeIntensity, b.fadeIntensity, t)
            );
        }
        
        public static CharacterAnimationData Zero => new CharacterAnimationData(0f, 0f, 0f, 0f);
        public static CharacterAnimationData One => new CharacterAnimationData(1f, 1f, 1f, 1f);
    }
    
    /// <summary>
    /// Animation types for easy preset application.
    /// </summary>
    public enum AnimationType
    {
        None,
        Shake,
        Wave,
        Scale,
        Fade,
        ColorShift,
        Typewriter,
        Pulse,
        Jitter,
        Custom
    }
    
    /// <summary>
    /// Preset animation configurations.
    /// </summary>
    [Serializable]
    public struct AnimationPreset
    {
        public AnimationType type;
        public float intensity;
        public float speed;
        public float frequency;
        public CharacterAnimationData characterData;
        
        public AnimationPreset(AnimationType animType, float animIntensity = 1f, float animSpeed = 1f, float animFrequency = 1f)
        {
            type = animType;
            intensity = animIntensity;
            speed = animSpeed;
            frequency = animFrequency;
            characterData = GetDefaultCharacterData(animType);
        }
        
        /// <summary>
        /// Gets default character animation data for a specific animation type.
        /// </summary>
        public static CharacterAnimationData GetDefaultCharacterData(AnimationType type)
        {
            switch (type)
            {
                case AnimationType.Shake:
                    return new CharacterAnimationData(1f, 0f, 0f, 1f);
                case AnimationType.Wave:
                    return new CharacterAnimationData(0f, 1f, 0f, 1f);
                case AnimationType.Scale:
                    return new CharacterAnimationData(0f, 0f, 1f, 1f);
                case AnimationType.Fade:
                    return new CharacterAnimationData(0f, 0f, 0f, 1f);
                case AnimationType.Typewriter:
                    return new CharacterAnimationData(0f, 0f, 0f, 0f);
                case AnimationType.Pulse:
                    return new CharacterAnimationData(0f, 0f, 1f, 1f);
                case AnimationType.Jitter:
                    return new CharacterAnimationData(1f, 1f, 0f, 1f);
                default:
                    return CharacterAnimationData.One;
            }
        }
        
        /// <summary>
        /// Applies this preset to a TextAnimationController.
        /// </summary>
        public void ApplyToController(TextAnimationController controller)
        {
            if (controller == null) return;
            
            // Reset all animations first
            controller.ResetAnimation();
            
            // Apply specific animation based on type
            switch (type)
            {
                case AnimationType.Shake:
                    controller.ShakeIntensity = intensity;
                    controller.ShakeSpeed = speed * 50f;
                    break;
                case AnimationType.Wave:
                    controller.WaveIntensity = intensity;
                    controller.WaveSpeed = speed * 20f;
                    controller.WaveFrequency = frequency * 2f;
                    break;
                case AnimationType.Scale:
                    controller.ScaleIntensity = intensity;
                    controller.ScaleSpeed = speed * 30f;
                    break;
                case AnimationType.Fade:
                    controller.FadeIntensity = intensity;
                    controller.FadeSpeed = speed * 25f;
                    break;
                case AnimationType.ColorShift:
                    controller.ColorShiftIntensity = intensity;
                    controller.ColorShiftSpeed = speed * 15f;
                    break;
                case AnimationType.Pulse:
                    controller.ScaleIntensity = intensity * 0.5f;
                    controller.ScaleSpeed = speed * 40f;
                    controller.FadeIntensity = intensity * 0.3f;
                    controller.FadeSpeed = speed * 35f;
                    break;
                case AnimationType.Jitter:
                    controller.ShakeIntensity = intensity * 0.7f;
                    controller.ShakeSpeed = speed * 80f;
                    controller.WaveIntensity = intensity * 0.3f;
                    controller.WaveSpeed = speed * 60f;
                    break;
            }
            
            // Apply character-specific data
            controller.SetAllCharactersAnimation(
                characterData.shakeIntensity,
                characterData.waveIntensity,
                characterData.scaleIntensity,
                characterData.fadeIntensity
            );
        }
    }
    
    /// <summary>
    /// Utility class for common animation operations.
    /// </summary>
    public static class TextAnimationUtility
    {
        /// <summary>
        /// Creates a typewriter effect by gradually enabling characters.
        /// </summary>
        public static void ApplyTypewriterEffect(TextAnimationController controller, float progress)
        {
            if (controller == null) return;
            
            controller.FadeIntensity = 1f;
            
            // Calculate how many characters should be visible
            int totalCharacters = controller.GetComponent<TMPro.TMP_Text>().textInfo.characterCount;
            int visibleCharacters = Mathf.RoundToInt(progress * totalCharacters);
            
            // Set character visibility
            for (int i = 0; i < totalCharacters; i++)
            {
                float fadeIntensity = i < visibleCharacters ? 1f : 0f;
                controller.SetCharacterAnimation(i, 1f, 1f, 1f, fadeIntensity);
            }
        }
        
        /// <summary>
        /// Creates a wave reveal effect.
        /// </summary>
        public static void ApplyWaveReveal(TextAnimationController controller, float progress, float waveWidth = 3f)
        {
            if (controller == null) return;
            
            controller.WaveIntensity = 2f;
            controller.FadeIntensity = 1f;
            
            int totalCharacters = controller.GetComponent<TMPro.TMP_Text>().textInfo.characterCount;
            float waveCenter = progress * totalCharacters;
            
            for (int i = 0; i < totalCharacters; i++)
            {
                float distance = Mathf.Abs(i - waveCenter);
                float waveIntensity = distance <= waveWidth ? 1f : 0f;
                float fadeIntensity = i <= waveCenter + waveWidth ? 1f : 0f;
                
                controller.SetCharacterAnimation(i, 0f, waveIntensity, 1f, fadeIntensity);
            }
        }
        
        /// <summary>
        /// Creates a shake emphasis effect on specific characters.
        /// </summary>
        public static void ApplyShakeEmphasis(TextAnimationController controller, int startIndex, int endIndex, float intensity = 1f)
        {
            if (controller == null) return;
            
            controller.ShakeIntensity = intensity * 3f;
            
            int totalCharacters = controller.GetComponent<TMPro.TMP_Text>().textInfo.characterCount;
            
            for (int i = 0; i < totalCharacters; i++)
            {
                float shakeIntensity = (i >= startIndex && i <= endIndex) ? 1f : 0f;
                controller.SetCharacterAnimation(i, shakeIntensity, 1f, 1f, 1f);
            }
        }
        
        /// <summary>
        /// Creates a rainbow color shift effect.
        /// </summary>
        public static void ApplyRainbowEffect(TextAnimationController controller, float speed = 1f)
        {
            if (controller == null) return;
            
            controller.ColorShiftIntensity = 1f;
            controller.ColorShiftSpeed = speed * 20f;
            
            // Each character gets a different hue offset
            int totalCharacters = controller.GetComponent<TMPro.TMP_Text>().textInfo.characterCount;
            
            for (int i = 0; i < totalCharacters; i++)
            {
                float hueOffset = (float)i / totalCharacters;
                controller.ColorShiftHue = hueOffset;
                controller.SetCharacterAnimation(i, 0f, 0f, 1f, 1f);
            }
        }
    }
}
