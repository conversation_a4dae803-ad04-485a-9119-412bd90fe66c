Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.42f1 (feb9a7235030) revision 16693671'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 32558 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\6000.0.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/Huggable X Horror
-logFile
Logs/AssetImportWorker1.log
-srvPort
64259
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: F:/Huggable X Horror
F:/Huggable X Horror
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [5016]  Target information:

Player connection [5016]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3638991710 [EditorId] 3638991710 [Version] 1048832 [Id] WindowsEditor(7,Marlon-PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5016] Host joined multi-casting on [***********:54997]...
Player connection [5016] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 6.48 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 4.31 ms.
Initialize engine version: 6000.0.42f1 (feb9a7235030)
[Subsystems] Discovering subsystems at path F:/6000.0.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Huggable X Horror/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 (ID=0x2504)
    Vendor:   NVIDIA
    VRAM:     12115 MB
    Driver:   32.0.15.7270
Initialize mono
Mono path[0] = 'F:/6000.0.42f1/Editor/Data/Managed'
Mono path[1] = 'F:/6000.0.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/6000.0.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56948
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/6000.0.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002439 seconds.
- Loaded All Assemblies, in  0.366 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.312 seconds
Domain Reload Profiling: 678ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (139ms)
		LoadAssemblies (134ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (135ms)
				TypeCache.ScanAssembly (124ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (312ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (259ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (132ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.034 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 3.80 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Huggable X Horror
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.350 seconds
Domain Reload Profiling: 2382ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (765ms)
		LoadAssemblies (487ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (370ms)
			TypeCache.Refresh (278ms)
				TypeCache.ScanAssembly (255ms)
			BuildScriptInfoCaches (76ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1350ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1173ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (188ms)
			ProcessInitializeOnLoadAttributes (606ms)
			ProcessInitializeOnLoadMethodAttributes (369ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 5.78 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.09 ms.
Unloading 335 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10024 unused Assets / (7.2 MB). Loaded Objects now: 10755.
Memory consumption went from 233.3 MB to 226.1 MB.
Total: 27.779800 ms (FindLiveObjects: 1.966300 ms CreateObjectMapping: 2.866100 ms MarkObjects: 14.798000 ms  DeleteObjects: 8.147600 ms)

========================================================================
Received Import Request.
  Time since last request: 161794.984465 seconds.
  path: Assets/GameManager/Text System/Base Visual Text Document.asset
  artifactKey: Guid(7321ef8c52aebb24fa4542168ad9b125) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameManager/Text System/Base Visual Text Document.asset using Guid(7321ef8c52aebb24fa4542168ad9b125) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Serialization depth limit 10 exceeded at 'UnityEngine.Events::ArgumentCache.m_ObjectArgument'. There may be an object composition cycle in one or more of your serialized classes. Consider rearranging data or use `[SerializeReference]`. 

Serialization hierarchy:
11: UnityEngine.Events::ArgumentCache.m_ObjectArgument
10: UnityEngine.Events::PersistentCall.m_Arguments
9: UnityEngine.Events::PersistentCallGroup.m_Calls
8: UnityEngine.Events::UnityEventBase.m_PersistentCalls
7: SentenceDisplayData.OnSentenceStart
6: SentenceDisplayData.ReplacementOptions
5: SentenceDisplayData.ReplacementOptions
4: SentenceDisplayData.ReplacementOptions
3: SentenceDisplayData.ReplacementOptions
2: SentenceDisplayData.ReplacementOptions
1: PhraseData.Sentences
0: VisualTextDocument.Phrases
Serialization depth limit 10 exceeded at 'UnityEngine.Events::ArgumentCache.m_ObjectArgument'. There may be an object composition cycle in one or more of your serialized classes. Consider rearranging data or use `[SerializeReference]`. 

Serialization hierarchy:
11: UnityEngine.Events::ArgumentCache.m_ObjectArgument
10: UnityEngine.Events::PersistentCall.m_Arguments
9: UnityEngine.Events::PersistentCallGroup.m_Calls
8: UnityEngine.Events::UnityEventBase.m_PersistentCalls
7: SentenceDisplayData.OnSentenceStart
6: SentenceDisplayData.ReplacementOptions
5: SentenceDisplayData.ReplacementOptions
4: SentenceDisplayData.ReplacementOptions
3: SentenceDisplayData.ReplacementOptions
2: SentenceDisplayData.ReplacementOptions
1: SentenceDisplayData.ReplacementOptions
0: PhraseData.Sentences
 -> (artifact id: '3d3b4b12af4e4c4782be761c11b6e9d8') in 0.0451303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.037 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.05 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.331 seconds
Domain Reload Profiling: 2372ms
	BeginReloadAssembly (252ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (693ms)
		LoadAssemblies (514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (269ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1331ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1094ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (458ms)
			ProcessInitializeOnLoadMethodAttributes (433ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 6.27 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 40 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10022 unused Assets / (8.4 MB). Loaded Objects now: 10777.
Memory consumption went from 215.0 MB to 206.6 MB.
Total: 25.285700 ms (FindLiveObjects: 1.877200 ms CreateObjectMapping: 2.535100 ms MarkObjects: 14.029500 ms  DeleteObjects: 6.842000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 197.857596 seconds.
  path: Assets/GameManager/Text System/Text Effects/Text Animation Effect/New Shake Effect.asset
  artifactKey: Guid(899c60a916815b94e910c9b455c526c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameManager/Text System/Text Effects/Text Animation Effect/New Shake Effect.asset using Guid(899c60a916815b94e910c9b455c526c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '38b80d6f5bae681e0c4a598d034acecb') in 0.0300781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 10.20 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.12 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9985 unused Assets / (7.0 MB). Loaded Objects now: 10777.
Memory consumption went from 190.7 MB to 183.8 MB.
Total: 30.295100 ms (FindLiveObjects: 2.155200 ms CreateObjectMapping: 2.520200 ms MarkObjects: 18.476100 ms  DeleteObjects: 7.140900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.24 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9985 unused Assets / (8.9 MB). Loaded Objects now: 10777.
Memory consumption went from 190.8 MB to 181.9 MB.
Total: 46.151200 ms (FindLiveObjects: 2.513700 ms CreateObjectMapping: 2.702100 ms MarkObjects: 29.901300 ms  DeleteObjects: 11.032400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.046 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.79 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.472 seconds
Domain Reload Profiling: 2522ms
	BeginReloadAssembly (237ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (729ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (290ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1472ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1204ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (260ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (405ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 8.03 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10020 unused Assets / (8.2 MB). Loaded Objects now: 10781.
Memory consumption went from 213.3 MB to 205.0 MB.
Total: 26.894200 ms (FindLiveObjects: 2.050200 ms CreateObjectMapping: 3.349200 ms MarkObjects: 13.990100 ms  DeleteObjects: 7.502900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1340.195240 seconds.
  path: Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Roboto-Bold SDF - Drop Shadow.mat
  artifactKey: Guid(b246c4190f4e46ec9352fe15a7b09ce0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Examples & Extras/Resources/Fonts & Materials/Roboto-Bold SDF - Drop Shadow.mat using Guid(b246c4190f4e46ec9352fe15a7b09ce0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95c58c5f8f6a71c2f7eafe4e5939a706') in 0.5212038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.26 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.07 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9983 unused Assets / (7.7 MB). Loaded Objects now: 10964.
Memory consumption went from 200.2 MB to 192.5 MB.
Total: 30.103300 ms (FindLiveObjects: 2.194200 ms CreateObjectMapping: 2.998800 ms MarkObjects: 17.476900 ms  DeleteObjects: 7.431200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1.847700 seconds.
  path: Assets/GameManager/Text System/Text Effects/Text Animation Effect/Continous Effects/ShakeAnimationEffect.cs
  artifactKey: Guid(d6dab1c893559d645adcdd35154891ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameManager/Text System/Text Effects/Text Animation Effect/Continous Effects/ShakeAnimationEffect.cs using Guid(d6dab1c893559d645adcdd35154891ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bcaa70b1f27d506500e13e8e17682b96') in 0.0171831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.280 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.06 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.246 seconds
Domain Reload Profiling: 2530ms
	BeginReloadAssembly (390ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (775ms)
		LoadAssemblies (622ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (295ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (267ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1246ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1015ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (199ms)
			ProcessInitializeOnLoadAttributes (453ms)
			ProcessInitializeOnLoadMethodAttributes (351ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 6.11 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.07 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10020 unused Assets / (8.4 MB). Loaded Objects now: 10849.
Memory consumption went from 219.3 MB to 210.9 MB.
Total: 23.677200 ms (FindLiveObjects: 1.904300 ms CreateObjectMapping: 2.242900 ms MarkObjects: 12.371800 ms  DeleteObjects: 7.156600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.044 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.31 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.323 seconds
Domain Reload Profiling: 2372ms
	BeginReloadAssembly (235ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (730ms)
		LoadAssemblies (516ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (306ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (279ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1324ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1071ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (203ms)
			ProcessInitializeOnLoadAttributes (473ms)
			ProcessInitializeOnLoadMethodAttributes (384ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 8.09 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.09 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10020 unused Assets / (7.5 MB). Loaded Objects now: 10855.
Memory consumption went from 219.1 MB to 211.6 MB.
Total: 26.778600 ms (FindLiveObjects: 2.066400 ms CreateObjectMapping: 2.943700 ms MarkObjects: 13.858300 ms  DeleteObjects: 7.908800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.128 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.51 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.306 seconds
Domain Reload Profiling: 2439ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (793ms)
		LoadAssemblies (558ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (338ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (293ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1307ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1050ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (192ms)
			ProcessInitializeOnLoadAttributes (477ms)
			ProcessInitializeOnLoadMethodAttributes (368ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 6.55 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.07 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10020 unused Assets / (8.5 MB). Loaded Objects now: 10861.
Memory consumption went from 219.1 MB to 210.6 MB.
Total: 25.417600 ms (FindLiveObjects: 1.936700 ms CreateObjectMapping: 2.639500 ms MarkObjects: 13.505800 ms  DeleteObjects: 7.334500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.210 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.47 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.552 seconds
Domain Reload Profiling: 2767ms
	BeginReloadAssembly (289ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (822ms)
		LoadAssemblies (588ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (353ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (308ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1552ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1292ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (204ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (504ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 7.41 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.09 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10018 unused Assets / (7.7 MB). Loaded Objects now: 10865.
Memory consumption went from 219.0 MB to 211.4 MB.
Total: 29.286800 ms (FindLiveObjects: 2.062100 ms CreateObjectMapping: 3.273200 ms MarkObjects: 15.148500 ms  DeleteObjects: 8.801300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.076 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.20 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.571 seconds
Domain Reload Profiling: 2650ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (734ms)
		LoadAssemblies (533ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (296ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (260ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1571ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1292ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (245ms)
			ProcessInitializeOnLoadAttributes (615ms)
			ProcessInitializeOnLoadMethodAttributes (419ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 7.00 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10021 unused Assets / (11.5 MB). Loaded Objects now: 10873.
Memory consumption went from 219.4 MB to 207.9 MB.
Total: 47.065400 ms (FindLiveObjects: 1.847500 ms CreateObjectMapping: 2.717000 ms MarkObjects: 17.612100 ms  DeleteObjects: 24.886500 ms)

Prepare: number of updated asset objects reloaded= 2
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.583 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.48 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.527 seconds
Domain Reload Profiling: 3116ms
	BeginReloadAssembly (375ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (1082ms)
		LoadAssemblies (807ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (465ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (404ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1527ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1217ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (222ms)
			ProcessInitializeOnLoadAttributes (538ms)
			ProcessInitializeOnLoadMethodAttributes (445ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 11.88 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.09 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10021 unused Assets / (8.5 MB). Loaded Objects now: 10877.
Memory consumption went from 219.2 MB to 210.7 MB.
Total: 28.568700 ms (FindLiveObjects: 2.003000 ms CreateObjectMapping: 3.761900 ms MarkObjects: 13.680300 ms  DeleteObjects: 9.121200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.27 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.09 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9966 unused Assets / (4.1 MB). Loaded Objects now: 10859.
Memory consumption went from 197.3 MB to 193.2 MB.
Total: 40.263500 ms (FindLiveObjects: 2.847800 ms CreateObjectMapping: 11.077100 ms MarkObjects: 18.750800 ms  DeleteObjects: 7.584800 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 7.52 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9957 unused Assets / (4.9 MB). Loaded Objects now: 10850.
Memory consumption went from 196.6 MB to 191.7 MB.
Total: 30.289600 ms (FindLiveObjects: 2.037100 ms CreateObjectMapping: 3.333700 ms MarkObjects: 15.467400 ms  DeleteObjects: 9.449600 ms)

Prepare: number of updated asset objects reloaded= 0
