using UnityEngine;
using TMPro;
using System.Collections.Generic;

namespace TextAnimation
{
    /// <summary>
    /// Manages material creation and property copying for text animation shaders.
    /// Automatically creates animation-enabled materials while preserving original font properties.
    /// </summary>
    public static class TextAnimationMaterialManager
    {
        private static readonly Dictionary<Material, Material> _materialCache = new Dictionary<Material, Material>();
        private static Shader _animationShader;
        
        private const string ANIMATION_SHADER_NAME = "TextMeshPro/Animated Text";
        
        // Standard TextMeshPro property names for copying
        private static readonly string[] STANDARD_PROPERTIES = {
            "_FaceColor", "_FaceDilate", "_OutlineColor", "_OutlineWidth", "_OutlineSoftness",
            "_UnderlayColor", "_UnderlayOffsetX", "_UnderlayOffsetY", "_UnderlayDilate", "_UnderlaySoftness",
            "_WeightNormal", "_WeightBold", "_MainTex", "_TextureWidth", "_TextureHeight",
            "_GradientScale", "_ScaleX", "_ScaleY", "_PerspectiveFilter", "_Sharpness",
            "_VertexOffsetX", "_VertexOffsetY", "_StencilComp", "_Stencil", "_StencilOp",
            "_StencilWriteMask", "_StencilReadMask", "_CullMode", "_ColorMask"
        };
        
        /// <summary>
        /// Gets the animation shader, loading it if necessary.
        /// </summary>
        public static Shader AnimationShader
        {
            get
            {
                if (_animationShader == null)
                {
                    _animationShader = Shader.Find(ANIMATION_SHADER_NAME);
                    if (_animationShader == null)
                    {
                        Debug.LogError($"Animation shader '{ANIMATION_SHADER_NAME}' not found! Make sure the shader is compiled and available.");
                    }
                }
                return _animationShader;
            }
        }
        
        /// <summary>
        /// Creates or retrieves an animation-enabled material based on the source material.
        /// Preserves all original properties while adding animation capabilities.
        /// </summary>
        /// <param name="sourceMaterial">The original TextMeshPro material</param>
        /// <returns>Animation-enabled material instance</returns>
        public static Material GetAnimationMaterial(Material sourceMaterial)
        {
            if (sourceMaterial == null)
            {
                Debug.LogWarning("Source material is null. Cannot create animation material.");
                return null;
            }
            
            // Check cache first
            if (_materialCache.TryGetValue(sourceMaterial, out Material cachedMaterial))
            {
                if (cachedMaterial != null)
                    return cachedMaterial;
                else
                    _materialCache.Remove(sourceMaterial); // Clean up null reference
            }
            
            // Create new animation material
            Material animationMaterial = CreateAnimationMaterial(sourceMaterial);
            if (animationMaterial != null)
            {
                _materialCache[sourceMaterial] = animationMaterial;
            }
            
            return animationMaterial;
        }
        
        /// <summary>
        /// Creates a new animation material by copying properties from the source material.
        /// </summary>
        private static Material CreateAnimationMaterial(Material sourceMaterial)
        {
            if (AnimationShader == null)
                return null;
                
            // Create new material with animation shader
            Material animationMaterial = new Material(AnimationShader);
            animationMaterial.name = $"{sourceMaterial.name}_Animated";
            
            // Copy all standard TextMeshPro properties
            CopyMaterialProperties(sourceMaterial, animationMaterial);
            
            // Initialize animation properties with default values
            InitializeAnimationProperties(animationMaterial);
            
            return animationMaterial;
        }
        
        /// <summary>
        /// Copies properties from source material to destination material.
        /// </summary>
        private static void CopyMaterialProperties(Material source, Material destination)
        {
            // Copy standard properties
            foreach (string propertyName in STANDARD_PROPERTIES)
            {
                if (source.HasProperty(propertyName) && destination.HasProperty(propertyName))
                {
                    CopyProperty(source, destination, propertyName);
                }
            }
            
            // Copy any additional properties that might exist
            CopyAdditionalProperties(source, destination);
        }
        
        /// <summary>
        /// Copies a specific property from source to destination material.
        /// </summary>
        private static void CopyProperty(Material source, Material destination, string propertyName)
        {
            try
            {
                var shader = source.shader;
                int propertyIndex = shader.FindPropertyIndex(propertyName);
                if (propertyIndex == -1) return;
                
                var propertyType = shader.GetPropertyType(propertyIndex);
                
                switch (propertyType)
                {
                    case UnityEngine.Rendering.ShaderPropertyType.Color:
                        destination.SetColor(propertyName, source.GetColor(propertyName));
                        break;
                    case UnityEngine.Rendering.ShaderPropertyType.Vector:
                        destination.SetVector(propertyName, source.GetVector(propertyName));
                        break;
                    case UnityEngine.Rendering.ShaderPropertyType.Float:
                    case UnityEngine.Rendering.ShaderPropertyType.Range:
                        destination.SetFloat(propertyName, source.GetFloat(propertyName));
                        break;
                    case UnityEngine.Rendering.ShaderPropertyType.Texture:
                        destination.SetTexture(propertyName, source.GetTexture(propertyName));
                        break;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to copy property '{propertyName}': {e.Message}");
            }
        }
        
        /// <summary>
        /// Copies additional properties that might not be in the standard list.
        /// </summary>
        private static void CopyAdditionalProperties(Material source, Material destination)
        {
            var sourceShader = source.shader;
            int propertyCount = sourceShader.GetPropertyCount();
            
            for (int i = 0; i < propertyCount; i++)
            {
                string propertyName = sourceShader.GetPropertyName(i);
                
                // Skip if already copied or if destination doesn't have this property
                if (System.Array.IndexOf(STANDARD_PROPERTIES, propertyName) != -1 || 
                    !destination.HasProperty(propertyName))
                    continue;
                    
                CopyProperty(source, destination, propertyName);
            }
        }
        
        /// <summary>
        /// Initializes animation properties with default values.
        /// </summary>
        private static void InitializeAnimationProperties(Material material)
        {
            // Set default animation values
            material.SetFloat("_AnimationTime", 0f);
            material.SetFloat("_ShakeIntensity", 0f);
            material.SetFloat("_ShakeSpeed", 50f);
            material.SetFloat("_WaveIntensity", 0f);
            material.SetFloat("_WaveSpeed", 20f);
            material.SetFloat("_WaveFrequency", 2f);
            material.SetFloat("_ScaleIntensity", 0f);
            material.SetFloat("_ScaleSpeed", 30f);
            material.SetFloat("_FadeIntensity", 0f);
            material.SetFloat("_FadeSpeed", 25f);
            material.SetFloat("_ColorShiftIntensity", 0f);
            material.SetFloat("_ColorShiftSpeed", 15f);
            material.SetFloat("_ColorShiftHue", 0f);
            material.SetFloat("_UsePerCharacterData", 1f);
        }
        
        /// <summary>
        /// Clears the material cache. Call this when materials are no longer needed.
        /// </summary>
        public static void ClearCache()
        {
            foreach (var kvp in _materialCache)
            {
                if (kvp.Value != null)
                {
                    if (Application.isPlaying)
                        Object.Destroy(kvp.Value);
                    else
                        Object.DestroyImmediate(kvp.Value);
                }
            }
            _materialCache.Clear();
        }
        
        /// <summary>
        /// Checks if a material is compatible with the animation system.
        /// </summary>
        public static bool IsCompatibleMaterial(Material material)
        {
            if (material == null) return false;
            
            // Check if it's already an animation material
            if (material.shader == AnimationShader) return true;
            
            // Check if it's a TextMeshPro material that can be converted
            string shaderName = material.shader.name;
            return shaderName.Contains("TextMeshPro") || shaderName.Contains("TMP");
        }
        
        /// <summary>
        /// Updates animation time for all cached materials.
        /// </summary>
        public static void UpdateAnimationTime(float time)
        {
            foreach (var material in _materialCache.Values)
            {
                if (material != null)
                {
                    material.SetFloat("_AnimationTime", time);
                }
            }
        }
    }
}
