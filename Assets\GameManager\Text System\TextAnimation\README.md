# Shader-Based Text Animation System

A comprehensive, high-performance text animation system for Unity that uses shader-based rendering instead of CPU mesh manipulation. This system provides smooth, efficient animations for TextMeshPro components with per-character control and easy-to-use presets.

## Features

- **Shader-Based Performance**: All animations run on the GPU for optimal performance
- **Per-Character Control**: Individual animation settings for each character
- **TextMeshPro Integration**: Seamless compatibility with TextMeshPro components
- **Material Preservation**: Automatically preserves original font properties
- **Easy-to-Use Presets**: Pre-built animation effects for common use cases
- **Dynamic Material Management**: Automatic material creation and property copying
- **Extension Methods**: Simple API for quick animation setup

## Quick Start

### Basic Setup

```csharp
using TextAnimation;
using TMPro;

// Get your TextMeshPro component
TMP_Text myText = GetComponent<TMP_Text>();

// Add animation capability
TextAnimationController controller = myText.AddAnimation();

// Set animation properties
controller.ShakeIntensity = 2f;
controller.WaveIntensity = 1f;
```

### Using Presets

```csharp
// Apply a preset animation
myText.AddAnimationPreset(AnimationType.Shake, intensity: 1f, speed: 1f);

// Or use extension methods for quick effects
myText.Shake(duration: 1f, intensity: 2f);
myText.Pulse(duration: 2f, intensity: 1.5f);
myText.Typewriter(speed: 0.05f);
```

## Animation Types

### Available Effects

1. **Shake**: Random character movement
2. **Wave**: Sine wave vertical movement
3. **Scale**: Character scaling animation
4. **Fade**: Alpha transparency animation
5. **ColorShift**: Hue shifting animation
6. **Typewriter**: Character-by-character reveal
7. **Pulse**: Combined scale and fade effect
8. **Jitter**: Combined shake and wave effect

### Per-Character Control

```csharp
TextAnimationController controller = myText.GetComponent<TextAnimationController>();

// Set animation for specific character (index, shake, wave, scale, fade)
controller.SetCharacterAnimation(0, 1f, 0f, 0f, 1f); // First character: shake only
controller.SetCharacterAnimation(1, 0f, 1f, 0f, 1f); // Second character: wave only

// Apply same settings to all characters
controller.SetAllCharactersAnimation(1f, 1f, 1f, 1f);
```

## Components

### TextAnimationController

The main component that manages shader properties and animation states.

**Key Properties:**
- `ShakeIntensity`, `ShakeSpeed`: Shake animation settings
- `WaveIntensity`, `WaveSpeed`, `WaveFrequency`: Wave animation settings
- `ScaleIntensity`, `ScaleSpeed`: Scale animation settings
- `FadeIntensity`, `FadeSpeed`: Fade animation settings
- `ColorShiftIntensity`, `ColorShiftSpeed`, `ColorShiftHue`: Color animation settings
- `UsePerCharacterData`: Enable/disable per-character control

**Key Methods:**
- `StartAnimation()`: Begin animation
- `StopAnimation()`: Stop animation
- `ResetAnimation()`: Reset all properties to default
- `SetCharacterAnimation()`: Set per-character animation data

### TextAnimationPresets

Provides easy-to-use preset animations and special effects.

**Key Methods:**
- `ApplyPreset(AnimationType)`: Apply a preset animation
- `StartTypewriter()`: Start typewriter effect
- `StartWaveReveal()`: Start wave reveal effect
- `ShakeWord(int wordIndex)`: Shake specific word
- `FadeIn(float duration)`: Fade in effect
- `FadeOut(float duration)`: Fade out effect

### TextAnimationMaterialManager

Handles automatic material creation and property preservation.

**Key Features:**
- Automatic detection of TextMeshPro materials
- Creation of animation-enabled material instances
- Preservation of original font properties
- Material caching for performance

## Advanced Usage

### Custom Animation Combinations

```csharp
TextAnimationController controller = myText.AddAnimation(false);

// Combine multiple effects
controller.ShakeIntensity = 1f;
controller.WaveIntensity = 0.5f;
controller.ColorShiftIntensity = 0.3f;

// Set different intensities per character
for (int i = 0; i < myText.text.Length; i++)
{
    float shakeIntensity = Mathf.Sin(i * 0.5f) * 0.5f + 0.5f;
    controller.SetCharacterAnimation(i, shakeIntensity, 1f, 1f, 1f);
}

controller.StartAnimation();
```

### Utility Functions

```csharp
// Typewriter effect with progress control
TextAnimationUtility.ApplyTypewriterEffect(controller, progress: 0.5f);

// Wave reveal effect
TextAnimationUtility.ApplyWaveReveal(controller, progress: 0.3f, waveWidth: 3f);

// Shake emphasis on character range
TextAnimationUtility.ApplyShakeEmphasis(controller, startIndex: 5, endIndex: 10);

// Rainbow color effect
TextAnimationUtility.ApplyRainbowEffect(controller, speed: 1f);
```

### Integration with Existing Systems

```csharp
// Setup animation on existing TextMeshPro component
TextAnimationController controller = TextAnimationIntegration.SetupTextAnimation(myText);

// Quick effects without permanent setup
TextAnimationIntegration.QuickShake(myText, duration: 0.5f, intensity: 2f);
TextAnimationIntegration.TypewriterEffect(myText, speed: 0.05f);
```

## Shader Properties

The animation shader supports all standard TextMeshPro properties plus:

- `_AnimationTime`: Current animation time
- `_ShakeIntensity`, `_ShakeSpeed`: Shake parameters
- `_WaveIntensity`, `_WaveSpeed`, `_WaveFrequency`: Wave parameters
- `_ScaleIntensity`, `_ScaleSpeed`: Scale parameters
- `_FadeIntensity`, `_FadeSpeed`: Fade parameters
- `_ColorShiftIntensity`, `_ColorShiftSpeed`, `_ColorShiftHue`: Color shift parameters
- `_UsePerCharacterData`: Enable per-character control

## Performance Considerations

- **GPU-Based**: All animations run on the GPU for optimal performance
- **Material Caching**: Materials are cached and reused to minimize memory usage
- **Efficient Updates**: Only vertex colors are updated when per-character data changes
- **Automatic Cleanup**: Materials are properly cleaned up when components are destroyed

## Requirements

- Unity 2020.3 or later
- TextMeshPro package
- Universal Render Pipeline (URP) or Built-in Render Pipeline

## Installation

1. Copy all files from the `TextAnimation` folder to your project
2. Ensure the shader `TMP_AnimatedText.shader` is compiled
3. Add the `TextAnimation` namespace to your scripts
4. Start using the animation system!

## Examples

See `TextAnimationDemo.cs` for a comprehensive example showing all animation types and usage patterns.

## Editor Integration

### Menu Items

The system provides Unity menu items for easy setup:

- `GameObject > UI > Text Animation > Add Animation Controller`: Adds animation to selected TextMeshPro
- `GameObject > UI > Text Animation > Add Preset Animation`: Adds preset animation component
- `Tools > Text Animation > Setup All TextMeshPro`: Automatically setup animation on all TextMeshPro components in scene

### Context Menu

Right-click on TextMeshPro components in the inspector for quick animation setup options.

## Troubleshooting

**Animation not working:**
- Ensure the TextMeshPro component has a valid material
- Check that the animation shader is compiled and available
- Verify that `StartAnimation()` has been called

**Material issues:**
- The system automatically creates compatible materials
- Original materials are preserved and restored when components are removed
- Check console for material-related warnings

**Performance issues:**
- Use per-character data sparingly for large texts
- Consider disabling `UsePerCharacterData` for simple animations
- Monitor material instances in the profiler
