using UnityEngine;
using TMPro;
using System.Collections;
using System.Collections.Generic;

namespace TextAnimation
{
    /// <summary>
    /// Main controller for shader-based text animations.
    /// Manages animation states, per-character data, and material properties.
    /// </summary>
    [RequireComponent(typeof(TMP_Text))]
    public class TextAnimationController : MonoBehaviour
    {
        [Header("Animation Settings")]
        [SerializeField] private bool _autoStartAnimation = true;
        [SerializeField] private bool _useUnscaledTime = false;
        
        [Header("Shake Animation")]
        [SerializeField] private float _shakeIntensity = 0f;
        [SerializeField] private float _shakeSpeed = 50f;
        
        [Header("Wave Animation")]
        [SerializeField] private float _waveIntensity = 0f;
        [SerializeField] private float _waveSpeed = 20f;
        [SerializeField] private float _waveFrequency = 2f;
        
        [Header("Scale Animation")]
        [SerializeField] private float _scaleIntensity = 0f;
        [SerializeField] private float _scaleSpeed = 30f;
        
        [Header("Fade Animation")]
        [SerializeField] private float _fadeIntensity = 0f;
        [SerializeField] private float _fadeSpeed = 25f;
        
        [Head<PERSON>("Color Animation")]
        [SerializeField] private float _colorShiftIntensity = 0f;
        [SerializeField] private float _colorShiftSpeed = 15f;
        [SerializeField] private float _colorShiftHue = 0f;
        
        [Header("Per-Character Control")]
        [SerializeField] private bool _usePerCharacterData = true;
        
        // Private fields
        private TMP_Text _textComponent;
        private Material _originalMaterial;
        private Material _animationMaterial;
        private float _animationTime = 0f;
        private bool _isAnimating = false;
        private Coroutine _animationCoroutine;
        
        // Per-character animation data
        private Color32[] _characterAnimationData;
        private bool _needsVertexUpdate = false;
        
        #region Properties
        
        public float ShakeIntensity
        {
            get => _shakeIntensity;
            set { _shakeIntensity = value; UpdateShaderProperty("_ShakeIntensity", value); }
        }
        
        public float ShakeSpeed
        {
            get => _shakeSpeed;
            set { _shakeSpeed = value; UpdateShaderProperty("_ShakeSpeed", value); }
        }
        
        public float WaveIntensity
        {
            get => _waveIntensity;
            set { _waveIntensity = value; UpdateShaderProperty("_WaveIntensity", value); }
        }
        
        public float WaveSpeed
        {
            get => _waveSpeed;
            set { _waveSpeed = value; UpdateShaderProperty("_WaveSpeed", value); }
        }
        
        public float WaveFrequency
        {
            get => _waveFrequency;
            set { _waveFrequency = value; UpdateShaderProperty("_WaveFrequency", value); }
        }
        
        public float ScaleIntensity
        {
            get => _scaleIntensity;
            set { _scaleIntensity = value; UpdateShaderProperty("_ScaleIntensity", value); }
        }
        
        public float ScaleSpeed
        {
            get => _scaleSpeed;
            set { _scaleSpeed = value; UpdateShaderProperty("_ScaleSpeed", value); }
        }
        
        public float FadeIntensity
        {
            get => _fadeIntensity;
            set { _fadeIntensity = value; UpdateShaderProperty("_FadeIntensity", value); }
        }
        
        public float FadeSpeed
        {
            get => _fadeSpeed;
            set { _fadeSpeed = value; UpdateShaderProperty("_FadeSpeed", value); }
        }
        
        public float ColorShiftIntensity
        {
            get => _colorShiftIntensity;
            set { _colorShiftIntensity = value; UpdateShaderProperty("_ColorShiftIntensity", value); }
        }
        
        public float ColorShiftSpeed
        {
            get => _colorShiftSpeed;
            set { _colorShiftSpeed = value; UpdateShaderProperty("_ColorShiftSpeed", value); }
        }
        
        public float ColorShiftHue
        {
            get => _colorShiftHue;
            set { _colorShiftHue = value; UpdateShaderProperty("_ColorShiftHue", value); }
        }
        
        public bool UsePerCharacterData
        {
            get => _usePerCharacterData;
            set { _usePerCharacterData = value; UpdateShaderProperty("_UsePerCharacterData", value ? 1f : 0f); }
        }
        
        public bool IsAnimating => _isAnimating;
        
        #endregion
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            _textComponent = GetComponent<TMP_Text>();
            if (_textComponent == null)
            {
                Debug.LogError("TextAnimationController requires a TMP_Text component!");
                enabled = false;
                return;
            }
        }
        
        private void Start()
        {
            InitializeAnimation();
            
            if (_autoStartAnimation)
            {
                StartAnimation();
            }
        }
        
        private void OnEnable()
        {
            if (_textComponent != null)
            {
                TMPro_EventManager.TEXT_CHANGED_EVENT.Add(OnTextChanged);
            }
        }
        
        private void OnDisable()
        {
            if (_textComponent != null)
            {
                TMPro_EventManager.TEXT_CHANGED_EVENT.Remove(OnTextChanged);
            }
            
            StopAnimation();
        }
        
        private void OnDestroy()
        {
            CleanupMaterials();
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// Starts the text animation.
        /// </summary>
        public void StartAnimation()
        {
            if (_isAnimating) return;
            
            _isAnimating = true;
            _animationCoroutine = StartCoroutine(AnimationLoop());
        }
        
        /// <summary>
        /// Stops the text animation.
        /// </summary>
        public void StopAnimation()
        {
            if (!_isAnimating) return;
            
            _isAnimating = false;
            if (_animationCoroutine != null)
            {
                StopCoroutine(_animationCoroutine);
                _animationCoroutine = null;
            }
        }
        
        /// <summary>
        /// Resets all animation properties to default values.
        /// </summary>
        public void ResetAnimation()
        {
            _animationTime = 0f;
            ShakeIntensity = 0f;
            WaveIntensity = 0f;
            ScaleIntensity = 0f;
            FadeIntensity = 0f;
            ColorShiftIntensity = 0f;
            UpdateShaderProperty("_AnimationTime", _animationTime);
        }
        
        /// <summary>
        /// Sets animation intensity for a specific character.
        /// </summary>
        /// <param name="characterIndex">Index of the character</param>
        /// <param name="shakeIntensity">Shake intensity (0-1)</param>
        /// <param name="waveIntensity">Wave intensity (0-1)</param>
        /// <param name="scaleIntensity">Scale intensity (0-1)</param>
        /// <param name="fadeIntensity">Fade intensity (0-1)</param>
        public void SetCharacterAnimation(int characterIndex, float shakeIntensity = 1f, float waveIntensity = 1f, 
            float scaleIntensity = 1f, float fadeIntensity = 1f)
        {
            if (!_usePerCharacterData) return;
            
            InitializeCharacterData();
            
            if (characterIndex >= 0 && characterIndex < _characterAnimationData.Length)
            {
                _characterAnimationData[characterIndex] = new Color32(
                    (byte)(Mathf.Clamp01(shakeIntensity) * 255),
                    (byte)(Mathf.Clamp01(waveIntensity) * 255),
                    (byte)(Mathf.Clamp01(scaleIntensity) * 255),
                    (byte)(Mathf.Clamp01(fadeIntensity) * 255)
                );
                _needsVertexUpdate = true;
            }
        }
        
        /// <summary>
        /// Applies animation settings to all characters.
        /// </summary>
        public void SetAllCharactersAnimation(float shakeIntensity = 1f, float waveIntensity = 1f, 
            float scaleIntensity = 1f, float fadeIntensity = 1f)
        {
            if (!_usePerCharacterData) return;
            
            InitializeCharacterData();
            
            Color32 animationData = new Color32(
                (byte)(Mathf.Clamp01(shakeIntensity) * 255),
                (byte)(Mathf.Clamp01(waveIntensity) * 255),
                (byte)(Mathf.Clamp01(scaleIntensity) * 255),
                (byte)(Mathf.Clamp01(fadeIntensity) * 255)
            );
            
            for (int i = 0; i < _characterAnimationData.Length; i++)
            {
                _characterAnimationData[i] = animationData;
            }
            _needsVertexUpdate = true;
        }
        
        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the animation system.
        /// </summary>
        private void InitializeAnimation()
        {
            if (_textComponent == null) return;

            // Store original material
            _originalMaterial = _textComponent.fontSharedMaterial;

            // Create animation material
            SetupAnimationMaterial();

            // Initialize shader properties
            UpdateAllShaderProperties();

            // Initialize per-character data if needed
            if (_usePerCharacterData)
            {
                InitializeCharacterData();
            }
        }

        /// <summary>
        /// Sets up the animation material.
        /// </summary>
        private void SetupAnimationMaterial()
        {
            if (_originalMaterial == null) return;

            _animationMaterial = TextAnimationMaterialManager.GetAnimationMaterial(_originalMaterial);
            if (_animationMaterial != null)
            {
                _textComponent.fontSharedMaterial = _animationMaterial;
            }
            else
            {
                Debug.LogWarning($"Failed to create animation material for {gameObject.name}");
            }
        }

        /// <summary>
        /// Updates all shader properties with current values.
        /// </summary>
        private void UpdateAllShaderProperties()
        {
            if (_animationMaterial == null) return;

            _animationMaterial.SetFloat("_AnimationTime", _animationTime);
            _animationMaterial.SetFloat("_ShakeIntensity", _shakeIntensity);
            _animationMaterial.SetFloat("_ShakeSpeed", _shakeSpeed);
            _animationMaterial.SetFloat("_WaveIntensity", _waveIntensity);
            _animationMaterial.SetFloat("_WaveSpeed", _waveSpeed);
            _animationMaterial.SetFloat("_WaveFrequency", _waveFrequency);
            _animationMaterial.SetFloat("_ScaleIntensity", _scaleIntensity);
            _animationMaterial.SetFloat("_ScaleSpeed", _scaleSpeed);
            _animationMaterial.SetFloat("_FadeIntensity", _fadeIntensity);
            _animationMaterial.SetFloat("_FadeSpeed", _fadeSpeed);
            _animationMaterial.SetFloat("_ColorShiftIntensity", _colorShiftIntensity);
            _animationMaterial.SetFloat("_ColorShiftSpeed", _colorShiftSpeed);
            _animationMaterial.SetFloat("_ColorShiftHue", _colorShiftHue);
            _animationMaterial.SetFloat("_UsePerCharacterData", _usePerCharacterData ? 1f : 0f);
        }

        /// <summary>
        /// Updates a specific shader property.
        /// </summary>
        private void UpdateShaderProperty(string propertyName, float value)
        {
            if (_animationMaterial != null)
            {
                _animationMaterial.SetFloat(propertyName, value);
            }
        }

        /// <summary>
        /// Initializes per-character animation data.
        /// </summary>
        private void InitializeCharacterData()
        {
            if (_textComponent == null) return;

            _textComponent.ForceMeshUpdate();
            TMP_TextInfo textInfo = _textComponent.textInfo;

            if (textInfo.characterCount > 0)
            {
                _characterAnimationData = new Color32[textInfo.characterCount];

                // Initialize with full intensity for all effects
                Color32 defaultData = new Color32(255, 255, 255, 255);
                for (int i = 0; i < _characterAnimationData.Length; i++)
                {
                    _characterAnimationData[i] = defaultData;
                }

                _needsVertexUpdate = true;
            }
        }

        /// <summary>
        /// Updates vertex colors with per-character animation data.
        /// </summary>
        private void UpdateVertexColors()
        {
            if (!_usePerCharacterData || _characterAnimationData == null || _textComponent == null)
                return;

            _textComponent.ForceMeshUpdate();
            TMP_TextInfo textInfo = _textComponent.textInfo;

            for (int i = 0; i < textInfo.characterCount && i < _characterAnimationData.Length; i++)
            {
                if (!textInfo.characterInfo[i].isVisible) continue;

                int materialIndex = textInfo.characterInfo[i].materialReferenceIndex;
                int vertexIndex = textInfo.characterInfo[i].vertexIndex;

                Color32[] colors = textInfo.meshInfo[materialIndex].colors32;
                Color32 animData = _characterAnimationData[i];

                // Apply animation data to all 4 vertices of the character
                if (vertexIndex + 3 < colors.Length)
                {
                    colors[vertexIndex + 0] = animData;
                    colors[vertexIndex + 1] = animData;
                    colors[vertexIndex + 2] = animData;
                    colors[vertexIndex + 3] = animData;
                }
            }

            // Update the mesh
            for (int i = 0; i < textInfo.meshInfo.Length; i++)
            {
                textInfo.meshInfo[i].mesh.colors32 = textInfo.meshInfo[i].colors32;
                _textComponent.UpdateGeometry(textInfo.meshInfo[i].mesh, i);
            }
        }

        /// <summary>
        /// Animation loop coroutine.
        /// </summary>
        private IEnumerator AnimationLoop()
        {
            while (_isAnimating)
            {
                // Update animation time
                _animationTime += _useUnscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
                UpdateShaderProperty("_AnimationTime", _animationTime);

                // Update vertex colors if needed
                if (_needsVertexUpdate)
                {
                    UpdateVertexColors();
                    _needsVertexUpdate = false;
                }

                yield return null;
            }
        }

        /// <summary>
        /// Called when text content changes.
        /// </summary>
        private void OnTextChanged(Object obj)
        {
            if (obj == _textComponent)
            {
                if (_usePerCharacterData)
                {
                    InitializeCharacterData();
                }
            }
        }

        /// <summary>
        /// Cleans up materials when destroyed.
        /// </summary>
        private void CleanupMaterials()
        {
            if (_textComponent != null && _originalMaterial != null)
            {
                _textComponent.fontSharedMaterial = _originalMaterial;
            }
        }

        #endregion
    }
}
