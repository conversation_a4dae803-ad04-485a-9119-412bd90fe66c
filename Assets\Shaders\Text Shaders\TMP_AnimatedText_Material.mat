%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TMP_AnimatedText_Material
  m_Shader: {fileID: 4800000, guid: 0000000000000000f000000000000000}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AnimationTime: 0
    - _ColorMask: 15
    - _ColorShiftHue: 0
    - _ColorShiftIntensity: 0
    - _ColorShiftSpeed: 15
    - _CullMode: 0
    - _FaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _FaceDilate: 0
    - _FadeIntensity: 0
    - _FadeSpeed: 25
    - _GradientScale: 5
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineSoftness: 0
    - _OutlineWidth: 0
    - _PerspectiveFilter: 0.875
    - _ScaleIntensity: 0
    - _ScaleSpeed: 30
    - _ScaleX: 1
    - _ScaleY: 1
    - _ShakeIntensity: 0
    - _ShakeSpeed: 50
    - _Sharpness: 0
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _TextureHeight: 512
    - _TextureWidth: 512
    - _UnderlayColor: {r: 0, g: 0, b: 0, a: 0.5}
    - _UnderlayDilate: 0
    - _UnderlayOffsetX: 0
    - _UnderlayOffsetY: 0
    - _UnderlaySoftness: 0
    - _UsePerCharacterData: 1
    - _VertexOffsetX: 0
    - _VertexOffsetY: 0
    - _WaveFrequency: 2
    - _WaveIntensity: 0
    - _WaveSpeed: 20
    - _WeightBold: 0.5
    - _WeightNormal: 0
    m_Colors:
    - _FaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _UnderlayColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_BuildTextureStacks: []
