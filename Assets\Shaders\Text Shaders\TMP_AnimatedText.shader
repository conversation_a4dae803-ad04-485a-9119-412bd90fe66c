Shader "TextMeshPro/Animated Text" {

Properties {
    // Standard TextMeshPro Properties
    _FaceColor          ("Face Color", Color) = (1,1,1,1)
    _FaceDilate         ("Face Dilate", Range(-1,1)) = 0

    _OutlineColor       ("Outline Color", Color) = (0,0,0,1)
    _OutlineWidth       ("Outline Thickness", Range(0, 1)) = 0
    _OutlineSoftness    ("Outline Softness", Range(0,1)) = 0

    _UnderlayColor      ("Border Color", Color) = (0,0,0, 0.5)
    _UnderlayOffsetX    ("Border OffsetX", Range(-1,1)) = 0
    _UnderlayOffsetY    ("Border OffsetY", Range(-1,1)) = 0
    _UnderlayDilate     ("Border Dilate", Range(-1,1)) = 0
    _UnderlaySoftness   ("Border Softness", Range(0,1)) = 0

    _WeightNormal       ("Weight Normal", float) = 0
    _WeightBold         ("Weight Bold", float) = 0.5

    _MainTex            ("Font Atlas", 2D) = "white" {}
    _TextureWidth       ("Texture Width", float) = 512
    _TextureHeight      ("Texture Height", float) = 512
    _GradientScale      ("Gradient Scale", float) = 5.0
    _ScaleX             ("Scale X", float) = 1.0
    _ScaleY             ("Scale Y", float) = 1.0
    _PerspectiveFilter  ("Perspective Correction", Range(0, 1)) = 0.875
    _Sharpness          ("Sharpness", Range(-1,1)) = 0

    _VertexOffsetX      ("Vertex OffsetX", float) = 0
    _VertexOffsetY      ("Vertex OffsetY", float) = 0

    // Animation Properties
    _AnimationTime      ("Animation Time", float) = 0
    
    // Shake Animation
    _ShakeIntensity     ("Shake Intensity", Range(0, 10)) = 0
    _ShakeSpeed         ("Shake Speed", Range(0, 100)) = 50
    
    // Wave Animation
    _WaveIntensity      ("Wave Intensity", Range(0, 10)) = 0
    _WaveSpeed          ("Wave Speed", Range(0, 100)) = 20
    _WaveFrequency      ("Wave Frequency", Range(0, 10)) = 2
    
    // Scale Animation
    _ScaleIntensity     ("Scale Intensity", Range(0, 5)) = 0
    _ScaleSpeed         ("Scale Speed", Range(0, 100)) = 30
    
    // Fade Animation
    _FadeIntensity      ("Fade Intensity", Range(0, 1)) = 0
    _FadeSpeed          ("Fade Speed", Range(0, 100)) = 25
    
    // Color Animation
    _ColorShiftIntensity ("Color Shift Intensity", Range(0, 1)) = 0
    _ColorShiftSpeed    ("Color Shift Speed", Range(0, 100)) = 15
    _ColorShiftHue      ("Color Shift Hue", Range(0, 1)) = 0
    
    // Per-Character Control
    _UsePerCharacterData ("Use Per-Character Data", Float) = 1
    
    // Stencil properties
    _StencilComp        ("Stencil Comparison", Float) = 8
    _Stencil            ("Stencil ID", Float) = 0
    _StencilOp          ("Stencil Operation", Float) = 0
    _StencilWriteMask   ("Stencil Write Mask", Float) = 255
    _StencilReadMask    ("Stencil Read Mask", Float) = 255

    _CullMode           ("Cull Mode", Float) = 0
    _ColorMask          ("Color Mask", Float) = 15
}

SubShader {
    Tags {
        "Queue"="Transparent"
        "IgnoreProjector"="True"
        "RenderType"="Transparent"
        "PreviewType"="Plane"
        "CanUseSpriteAtlas"="True"
    }

    Stencil {
        Ref [_Stencil]
        Comp [_StencilComp]
        Pass [_StencilOp]
        ReadMask [_StencilReadMask]
        WriteMask [_StencilWriteMask]
    }

    Cull [_CullMode]
    ZWrite Off
    Lighting Off
    Fog { Mode Off }
    ZTest [unity_GUIZTestMode]
    Blend SrcAlpha OneMinusSrcAlpha
    ColorMask [_ColorMask]

    Pass {
        CGPROGRAM
        #pragma vertex VertShader
        #pragma fragment PixShader
        #pragma shader_feature __ OUTLINE_ON
        #pragma shader_feature __ UNDERLAY_ON UNDERLAY_INNER

        #pragma multi_compile __ UNITY_UI_CLIP_RECT
        #pragma multi_compile __ UNITY_UI_ALPHACLIP

        #include "UnityCG.cginc"
        #include "UnityUI.cginc"
        #include "TMPro_Properties.cginc"
        #include "TMPro.cginc"

        struct vertex_t {
            UNITY_VERTEX_INPUT_INSTANCE_ID
            float4  vertex              : POSITION;
            float3  normal              : NORMAL;
            float4  color               : COLOR;
            float2  texcoord0           : TEXCOORD0;
            float2  texcoord1           : TEXCOORD1;
        };

        struct pixel_t {
            UNITY_VERTEX_INPUT_INSTANCE_ID
            UNITY_VERTEX_OUTPUT_STEREO
            float4  vertex              : SV_POSITION;
            float4  faceColor           : COLOR;
            float4  outlineColor        : COLOR1;
            float4  texcoord0           : TEXCOORD0;
            half4   param               : TEXCOORD1;
            #if (UNDERLAY_ON | UNDERLAY_INNER)
            float4  texcoord2           : TEXCOORD2;
            #endif
            float4  mask                : TEXCOORD3;
            float   characterIndex      : TEXCOORD4;
        };

        // Animation Properties
        float _AnimationTime;
        float _ShakeIntensity;
        float _ShakeSpeed;
        float _WaveIntensity;
        float _WaveSpeed;
        float _WaveFrequency;
        float _ScaleIntensity;
        float _ScaleSpeed;
        float _FadeIntensity;
        float _FadeSpeed;
        float _ColorShiftIntensity;
        float _ColorShiftSpeed;
        float _ColorShiftHue;
        float _UsePerCharacterData;

        // Utility function to convert HSV to RGB
        float3 HSVtoRGB(float3 hsv) {
            float4 K = float4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
            float3 p = abs(frac(hsv.xxx + K.xyz) * 6.0 - K.www);
            return hsv.z * lerp(K.xxx, clamp(p - K.xxx, 0.0, 1.0), hsv.y);
        }

        // Function to extract character index from vertex position
        float GetCharacterIndex(float4 vertex, float2 texcoord) {
            // Use UV.x coordinate scaled by texture width to approximate character index
            return floor(texcoord.x * _TextureWidth * 0.01);
        }

        pixel_t VertShader(vertex_t input) {
            pixel_t output;

            UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
            UNITY_TRANSFER_INSTANCE_ID(input, output);

            float bold = step(input.texcoord1.y, 0);

            float4 vert = input.vertex;
            vert.x += _VertexOffsetX;
            vert.y += _VertexOffsetY;

            // Extract character index and per-character data
            float charIndex = GetCharacterIndex(vert, input.texcoord0);
            output.characterIndex = charIndex;
            
            // Per-character animation intensities (stored in vertex color channels)
            float shakeIntensity = 1.0;
            float waveIntensity = 1.0;
            float scaleIntensity = 1.0;
            float fadeIntensity = 1.0;
            
            if (_UsePerCharacterData > 0.5) {
                shakeIntensity = input.color.r;  // Red channel for shake
                waveIntensity = input.color.g;   // Green channel for wave
                scaleIntensity = input.color.b;  // Blue channel for scale
                fadeIntensity = input.color.a;   // Alpha channel for fade
            }

            // Apply shake animation
            if (_ShakeIntensity > 0) {
                float effectiveShake = _ShakeIntensity * shakeIntensity;
                float shakeX = sin(_AnimationTime * _ShakeSpeed + charIndex * 0.5) * effectiveShake * 0.01;
                float shakeY = cos(_AnimationTime * _ShakeSpeed * 1.1 + charIndex * 0.7) * effectiveShake * 0.01;
                vert.x += shakeX;
                vert.y += shakeY;
            }

            // Apply wave animation
            if (_WaveIntensity > 0) {
                float effectiveWave = _WaveIntensity * waveIntensity;
                float waveOffset = sin(_AnimationTime * _WaveSpeed + charIndex * _WaveFrequency) * effectiveWave * 0.01;
                vert.y += waveOffset;
            }

            // Apply scale animation
            if (_ScaleIntensity > 0) {
                float effectiveScale = _ScaleIntensity * scaleIntensity;
                float scaleMultiplier = 1.0 + sin(_AnimationTime * _ScaleSpeed + charIndex * 0.3) * effectiveScale * 0.1;
                vert.xy *= scaleMultiplier;
            }

            float4 vPosition = UnityObjectToClipPos(vert);

            float2 pixelSize = vPosition.w;
            pixelSize /= float2(_ScaleX, _ScaleY) * abs(mul((float2x2)UNITY_MATRIX_P, _ScreenParams.xy));

            float scale = rsqrt(dot(pixelSize, pixelSize));
            scale *= abs(input.texcoord1.y) * _GradientScale * (_Sharpness + 1);
            if(UNITY_MATRIX_P[3][3] == 0) scale = lerp(abs(scale) * (1 - _PerspectiveFilter), scale, abs(dot(UnityObjectToWorldNormal(input.normal.xyz), normalize(WorldSpaceViewDir(vert)))));

            float weight = lerp(_WeightNormal, _WeightBold, bold) / 4.0;
            weight = (weight + _FaceDilate) * _ScaleRatioA * 0.5;

            float layerScale = scale;

            scale /= 1 + (_OutlineSoftness * _ScaleRatioA * scale);
            layerScale /= 1 + ((_UnderlaySoftness * _ScaleRatioB) * layerScale);

            float bias = (0.5 - weight) * scale - 0.5;
            float outline = _OutlineWidth * _ScaleRatioA * 0.5 * scale;

            float opacity = input.color.a;
            #if (UNDERLAY_ON | UNDERLAY_INNER)
            opacity = 1.0;
            #endif

            fixed4 faceColor = fixed4(input.color.rgb, opacity) * _FaceColor;
            faceColor.rgb *= faceColor.a;

            fixed4 outlineColor = _OutlineColor;
            outlineColor.a *= opacity;
            outlineColor.rgb *= outlineColor.a;
            outlineColor = lerp(faceColor, outlineColor, sqrt(min(1.0, (outline * 2))));

            #if (UNDERLAY_ON | UNDERLAY_INNER)
            layerScale += 1;
            bias -= 0.5;
            float sd = (bias - outline * 0.5) * layerScale + 0.5;
            float2 layerOffset = float2(_UnderlayOffsetX, _UnderlayOffsetY) * _ScaleRatioC;
            output.texcoord2 = float4((input.texcoord0 + layerOffset) * layerScale, input.texcoord0 * scale);
            output.param.z = layerScale;
            output.param.w = sd;
            #endif

            output.vertex = vPosition;
            output.faceColor = faceColor;
            output.outlineColor = outlineColor;
            output.texcoord0 = float4(input.texcoord0, scale, bias);
            output.param.xy = float2(outline, bias);

            #if UNITY_UI_CLIP_RECT
            output.mask = half4(vert.xy * 2 - clamp(vert.xy * 2, clipRect.xy, clipRect.zw), 0.25 / (0.25 * half2(_UIMaskSoftnessX, _UIMaskSoftnessY) + abs(pixelSize.xy)));
            #endif

            return output;
        }

        fixed4 PixShader(pixel_t input) : SV_Target {
            UNITY_SETUP_INSTANCE_ID(input);

            float c = tex2D(_MainTex, input.texcoord0.xy).a;

            #ifndef UNDERLAY_ON
            clip(c - 0.001);
            #endif

            float scale = input.texcoord0.z;
            float bias = input.texcoord0.w;
            float outline = input.param.x;
            float softness = input.param.y;

            float sd = (bias - c) * scale + 0.5;
            float outline_sd = sd - outline;

            float face_alpha = 1-saturate((sd + softness) * 0.5/softness);
            float outline_alpha = 1-saturate((outline_sd + softness) * 0.5/softness);

            fixed4 color = input.faceColor * face_alpha + input.outlineColor * (outline_alpha - face_alpha);

            // Apply fade animation
            if (_FadeIntensity > 0) {
                float fadeIntensity = 1.0;
                if (_UsePerCharacterData > 0.5) {
                    fadeIntensity = input.faceColor.a; // Use original alpha as fade intensity
                }
                float fadeEffect = sin(_AnimationTime * _FadeSpeed + input.characterIndex * 0.4) * 0.5 + 0.5;
                fadeEffect = lerp(1.0, fadeEffect, _FadeIntensity * fadeIntensity);
                color.a *= fadeEffect;
            }

            // Apply color shift animation
            if (_ColorShiftIntensity > 0) {
                float3 hsv = float3(_ColorShiftHue + sin(_AnimationTime * _ColorShiftSpeed + input.characterIndex * 0.6) * 0.1, 1.0, 1.0);
                float3 shiftedColor = HSVtoRGB(hsv);
                color.rgb = lerp(color.rgb, color.rgb * shiftedColor, _ColorShiftIntensity);
            }

            #if (UNDERLAY_ON | UNDERLAY_INNER)
            float d = tex2D(_MainTex, input.texcoord2.zw).a * input.param.z;
            d = (input.param.w - d) * input.param.z + 0.5;

            #if UNDERLAY_ON
            d = max(d, 1-outline_alpha);
            #else
            d = (1-d) * outline_alpha;
            #endif

            d = 1-saturate(d - input.param.y);

            fixed4 underlay = _UnderlayColor;
            underlay.rgb *= underlay.a;

            color += underlay * d * (1-color.a);
            #endif

            #if UNITY_UI_CLIP_RECT
            color.a *= UnityGet2DClipping(input.mask.xy, input.mask.zw);
            #endif

            #if UNITY_UI_ALPHACLIP
            clip(color.a - 0.001);
            #endif

            return color;
        }
        ENDCG
    }
}

CustomEditor "TMPro.EditorUtilities.TMP_SDFShaderGUI"
}
