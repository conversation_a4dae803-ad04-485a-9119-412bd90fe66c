using UnityEngine;
using TMPro;
using UnityEngine.UI;

namespace TextAnimation.Examples
{
    /// <summary>
    /// Demonstration script showing various text animation effects.
    /// Use this as a reference for implementing text animations in your projects.
    /// </summary>
    public class TextAnimationDemo : MonoBehaviour
    {
        [Header("Text Components")]
        [SerializeField] private TMP_Text _titleText;
        [SerializeField] private TMP_Text _shakeText;
        [SerializeField] private TMP_Text _waveText;
        [SerializeField] private TMP_Text _typewriterText;
        [SerializeField] private TMP_Text _pulseText;
        [SerializeField] private TMP_Text _rainbowText;
        [SerializeField] private TMP_Text _customText;
        
        [Header("Control Buttons")]
        [SerializeField] private Button _startAllButton;
        [SerializeField] private Button _stopAllButton;
        [SerializeField] private Button _resetAllButton;
        [SerializeField] private Button _typewriterButton;
        [SerializeField] private Button _waveRevealButton;
        
        [Header("Animation Settings")]
        [SerializeField] private float _animationIntensity = 1f;
        [SerializeField] private float _animationSpeed = 1f;
        
        private TextAnimationController[] _controllers;
        private TextAnimationPresets[] _presets;
        
        private void Start()
        {
            SetupAnimations();
            SetupButtons();
            StartDemoAnimations();
        }
        
        /// <summary>
        /// Sets up all text animations with different effects.
        /// </summary>
        private void SetupAnimations()
        {
            // Title with pulse effect
            if (_titleText != null)
            {
                _titleText.AddAnimationPreset(AnimationType.Pulse, 0.3f, 0.8f);
            }
            
            // Shake text
            if (_shakeText != null)
            {
                _shakeText.AddAnimationPreset(AnimationType.Shake, _animationIntensity, _animationSpeed * 2f);
            }
            
            // Wave text
            if (_waveText != null)
            {
                _waveText.AddAnimationPreset(AnimationType.Wave, _animationIntensity, _animationSpeed);
            }
            
            // Typewriter text (will be triggered manually)
            if (_typewriterText != null)
            {
                _typewriterText.AddAnimationPreset(AnimationType.Typewriter, 1f, 0.05f);
            }
            
            // Pulse text
            if (_pulseText != null)
            {
                _pulseText.AddAnimationPreset(AnimationType.Pulse, _animationIntensity * 0.5f, _animationSpeed * 1.5f);
            }
            
            // Rainbow text
            if (_rainbowText != null)
            {
                _rainbowText.AddAnimationPreset(AnimationType.ColorShift, 1f, _animationSpeed * 0.5f);
            }
            
            // Custom animation example
            if (_customText != null)
            {
                SetupCustomAnimation();
            }
            
            // Cache all controllers and presets
            _controllers = FindObjectsOfType<TextAnimationController>();
            _presets = FindObjectsOfType<TextAnimationPresets>();
        }
        
        /// <summary>
        /// Sets up a custom animation combining multiple effects.
        /// </summary>
        private void SetupCustomAnimation()
        {
            TextAnimationController controller = _customText.AddAnimation(false);
            
            // Combine shake and wave effects
            controller.ShakeIntensity = 0.5f;
            controller.ShakeSpeed = 80f;
            controller.WaveIntensity = 1f;
            controller.WaveSpeed = 30f;
            controller.WaveFrequency = 1.5f;
            controller.ColorShiftIntensity = 0.3f;
            controller.ColorShiftSpeed = 20f;
            
            // Set per-character animation data for variety
            for (int i = 0; i < _customText.text.Length; i++)
            {
                float shakeIntensity = Mathf.Sin(i * 0.5f) * 0.5f + 0.5f;
                float waveIntensity = Mathf.Cos(i * 0.3f) * 0.5f + 0.5f;
                controller.SetCharacterAnimation(i, shakeIntensity, waveIntensity, 1f, 1f);
            }
            
            controller.StartAnimation();
        }
        
        /// <summary>
        /// Sets up button event handlers.
        /// </summary>
        private void SetupButtons()
        {
            if (_startAllButton != null)
                _startAllButton.onClick.AddListener(StartAllAnimations);
                
            if (_stopAllButton != null)
                _stopAllButton.onClick.AddListener(StopAllAnimations);
                
            if (_resetAllButton != null)
                _resetAllButton.onClick.AddListener(ResetAllAnimations);
                
            if (_typewriterButton != null)
                _typewriterButton.onClick.AddListener(StartTypewriterEffect);
                
            if (_waveRevealButton != null)
                _waveRevealButton.onClick.AddListener(StartWaveRevealEffect);
        }
        
        /// <summary>
        /// Starts the demo animations.
        /// </summary>
        private void StartDemoAnimations()
        {
            // Start most animations immediately, except typewriter
            foreach (var controller in _controllers)
            {
                if (controller.gameObject != _typewriterText?.gameObject)
                {
                    controller.StartAnimation();
                }
            }
        }
        
        #region Button Event Handlers
        
        public void StartAllAnimations()
        {
            foreach (var controller in _controllers)
            {
                controller.StartAnimation();
            }
        }
        
        public void StopAllAnimations()
        {
            foreach (var controller in _controllers)
            {
                controller.StopAnimation();
            }
        }
        
        public void ResetAllAnimations()
        {
            foreach (var controller in _controllers)
            {
                controller.ResetAnimation();
            }
        }
        
        public void StartTypewriterEffect()
        {
            if (_typewriterText != null)
            {
                var presets = _typewriterText.GetComponent<TextAnimationPresets>();
                if (presets != null)
                {
                    presets.StartTypewriter();
                }
            }
        }
        
        public void StartWaveRevealEffect()
        {
            if (_waveText != null)
            {
                var presets = _waveText.GetComponent<TextAnimationPresets>();
                if (presets != null)
                {
                    presets.StartWaveReveal();
                }
            }
        }
        
        #endregion
        
        #region Public Methods for UI Controls
        
        /// <summary>
        /// Changes animation intensity for all effects.
        /// </summary>
        public void SetAnimationIntensity(float intensity)
        {
            _animationIntensity = intensity;
            
            foreach (var presets in _presets)
            {
                presets.Intensity = intensity;
            }
        }
        
        /// <summary>
        /// Changes animation speed for all effects.
        /// </summary>
        public void SetAnimationSpeed(float speed)
        {
            _animationSpeed = speed;
            
            foreach (var presets in _presets)
            {
                presets.Speed = speed;
            }
        }
        
        /// <summary>
        /// Demonstrates quick effect methods.
        /// </summary>
        public void DemonstrateQuickEffects()
        {
            if (_titleText != null)
            {
                _titleText.Shake(1f, 2f);
            }
            
            if (_customText != null)
            {
                _customText.Pulse(2f, 1.5f);
            }
        }
        
        /// <summary>
        /// Shows how to animate specific words.
        /// </summary>
        public void AnimateSpecificWords()
        {
            if (_customText != null)
            {
                var presets = _customText.GetComponent<TextAnimationPresets>();
                if (presets != null)
                {
                    // Shake the first word
                    presets.ShakeWord(0, 1f, 2f);
                }
            }
        }
        
        /// <summary>
        /// Demonstrates character-specific animations.
        /// </summary>
        public void AnimateSpecificCharacters()
        {
            if (_customText != null)
            {
                var controller = _customText.GetComponent<TextAnimationController>();
                if (controller != null)
                {
                    // Animate every other character differently
                    for (int i = 0; i < _customText.text.Length; i++)
                    {
                        if (i % 2 == 0)
                        {
                            controller.SetCharacterAnimation(i, 1f, 0f, 0f, 1f); // Only shake
                        }
                        else
                        {
                            controller.SetCharacterAnimation(i, 0f, 1f, 0f, 1f); // Only wave
                        }
                    }
                }
            }
        }
        
        #endregion
        
        #region Inspector Validation
        
        private void OnValidate()
        {
            // Update animation settings when values change in inspector
            if (Application.isPlaying && _presets != null)
            {
                foreach (var presets in _presets)
                {
                    if (presets != null)
                    {
                        presets.Intensity = _animationIntensity;
                        presets.Speed = _animationSpeed;
                    }
                }
            }
        }
        
        #endregion
    }
}
